import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
  // Clear existing data
  await prisma.project.deleteMany();
  await prisma.skill.deleteMany();
  await prisma.softSkill.deleteMany();
  await prisma.education.deleteMany();
  await prisma.user.deleteMany();

  // Create admin user
  await prisma.user.create({
    data: {
      email: "<EMAIL>",
      name: "Admin",
      role: "admin",
    },
  });

  // Seed projects from existing data
  const projects = [
    {
      name: "LLM Annotator",
      description:
        "A comprehensive tool for annotating and managing datasets for Large Language Model training with advanced labeling capabilities.",
      content: JSON.stringify([
        "Dataset Management: Organize and manage large datasets for LLM training",
        "Advanced Labeling: Multi-class and multi-label annotation capabilities",
        "Quality Control: Built-in validation and quality assurance tools",
        "Export Options: Multiple format support for different ML frameworks",
        "Collaborative Features: Team-based annotation with role management",
        "Progress Tracking: Real-time monitoring of annotation progress",
        "Integration Ready: API support for seamless workflow integration",
      ]),
      images: JSON.stringify([
        "/assets/images/projects/llm_annotator_1.webp",
        "/assets/images/projects/llm_annotator_2.webp",
      ]),
      videos: JSON.stringify([]),
      technologies: JSON.stringify([
        "python",
        "streamlit",
        "pandas",
        "numpy",
        "jupyter",
        "git",
        "vscode",
      ]),
      category: "Machine Learning and Data Annotation",
      link: "https://github.com/Anas-Altaf/LLM-Annotator",
      featured: true,
      order: 1,
    },
    {
      name: "Motion Media - Control Media Play with Hand Gestures",
      description:
        "A Python application that allows you to control media players using hand gestures, face detection, and voice commands.",
      content: JSON.stringify([
        "Gesture Control: Play/pause, seek, volume, screenshot using hand gestures",
        "Face Detection: Auto-pauses media when you look away from the screen",
        "Voice Commands: Natural language control for media playback",
        "Multi-Platform Support: Works with various media players and applications",
        "Real-time Processing: Low-latency gesture recognition for smooth control",
        "Customizable Gestures: Configure your own gesture mappings",
        "Privacy-Focused: All processing happens locally on your device",
      ]),
      images: JSON.stringify(["/assets/images/projects/motion_media_ss_1.jpg"]),
      videos: JSON.stringify(["/assets/videos/projects/motion_media_vid.mp4"]),
      technologies: JSON.stringify([
        "python",
        "numpy",
        "pypi",
        "opencv",
        "git",
        "vscode",
      ]),
      category: "Computer Vision and Gesture Recognition",
      link: "https://github.com/Anas-Altaf/Motion_Media",
      featured: true,
      order: 2,
    },
    {
      name: "WhatPoint - AI-Powered WhatsApp Assistant",
      description:
        "App to WhatsApp into an intelligent assistant with voice, task and email management and more using LangChain and LLMs.",
      content: JSON.stringify([
        "WhatsApp Integration: Seamlessly responds to text and voice messages",
        "Voice Recognition: Transcribes audio messages to text for processing",
        "Task Management: Create, read, update and delete personal tasks",
        "Gmail Integration: Send emails and retrieve recent inbox messages",
        "LangChain Framework: Advanced AI conversation capabilities",
        "Multi-Modal Support: Handle text, voice, and image inputs",
        "Context Awareness: Maintains conversation context for better responses",
      ]),
      images: JSON.stringify(["/assets/images/projects/whatpoint.webp"]),
      videos: JSON.stringify(["/assets/videos/projects/mld_vid.mp4"]),
      technologies: JSON.stringify([
        "python",
        "fastapi",
        "pypi",
        "android",
        "git",
        "vscode",
      ]),
      category: "AI Agents and Automation",
      link: "https://github.com/Anas-Altaf/WhatPoint",
      featured: true,
      order: 3,
    },
    {
      name: "Multilingual Dictionary",
      description:
        "A comprehensive Java-based dictionary application supporting multiple languages with advanced search capabilities and morphological analysis.",
      content: JSON.stringify([
        "Multi-language word lookups by word, root, or meaning.",
        "Fuzzy matching and intelligent word segmentation.",
        "Morphological analysis, part-of-speech tagging, and lemmatization.",
        "MySQL database for scalable storage with CRUD operations.",
        "Professional interface with Java Swing and FlatLaf.",
      ]),
      images: JSON.stringify([
        "/assets/images/projects/mld_ss_1.webp",
        "/assets/images/projects/mld_ss_2.webp",
      ]),
      videos: JSON.stringify(["/assets/videos/projects/mld_vid.mp4"]),
      technologies: JSON.stringify([
        "java",
        "intellij",
        "github",
        "git",
        "maven",
        "mysql",
        "apache",
      ]),
      category: "Desktop Development with Java",
      link: "https://github.com/Anas-Altaf/Multilingual-Dictionary",
      featured: false,
      order: 4,
    },
    {
      name: "Neurl-PS Scrapper",
      description:
        "A robust scrapper for https://nips.papers.cc , it could be helpful if you are a researcher or training your LLMs.",
      content: JSON.stringify([
        "Neurl-PS-Scraper_py is a Python-based tool to easily scrape NeurIPS papers.",
        "Features an intuitive GUI for easy navigation and searching of papers.",
        "Efficient Scraping: Fetches paper metadata and PDFs quickly.",
        "Progress Tracking: Allows real-time monitoring of the scraping process.",
        "Metadata Storage: Saves and manages paper details for future reference.",
        "Run the tool with either the CLI version or the GUI version.",
        "Search for papers, download PDFs, and view metadata.",
      ]),
      images: JSON.stringify([
        "/assets/images/projects/nips_ss_1.webp",
        "/assets/images/projects/nips_ss_2.webp",
      ]),
      videos: JSON.stringify(["/assets/videos/projects/nips_vid.mp4"]),
      technologies: JSON.stringify([
        "python",
        "jupyter",
        "selenium",
        "pandas",
        "streamlit",
        "git",
        "pycharm",
      ]),
      category: "Web Scraping and Data Science",
      link: "https://github.com/Anas-Altaf/Neurl-PS-Scraper_py",
      featured: false,
      order: 5,
    },
    {
      name: "Railway Management System",
      description:
        "A comprehensive C# application designed to streamline railway operations by managing train schedules, reservations, and user information.",
      content: JSON.stringify([
        "Train Management: Add, update, and delete train schedules with real-time tracking",
        "Reservation System: Complete booking system with seat selection and payment processing",
        "User Management: Role-based access control for passengers, staff, and administrators",
        "Database Integration: Robust SQL Server integration for data persistence and reporting",
        "Reporting Module: Generate comprehensive reports for operations and financial analysis",
        "Security Features: Encrypted user authentication and secure data handling",
        "Intuitive Interface: User-friendly C# application with responsive design patterns",
      ]),
      images: JSON.stringify([
        "/assets/images/projects/rm_1.webp",
        "/assets/images/projects/rm_2.webp",
      ]),
      videos: JSON.stringify([]),
      technologies: JSON.stringify([
        "csharp",
        "dotnetcore",
        "visualstudio",
        "microsoftsqlserver",
        "git",
      ]),
      category: "Desktop Application Development",
      link: "https://github.com/Anas-Altaf/Railway-Management-System",
      featured: false,
      order: 6,
    },
  ];

  for (const project of projects) {
    await prisma.project.create({ data: project });
  }

  // Seed skills
  const skills = [
    {
      title: "Frontend Development",
      description:
        "Skilled in creating dynamic UIs across web, mobile and desktop platforms.",
      technologies: JSON.stringify([
        "React",
        "Flutter",
        "Streamlit",
        "Swing",
        "Gradio",
        "Next.js",
        "Winforms",
      ]),
      category: "Development",
      order: 1,
    },
    {
      title: "AI and Automation",
      description:
        "Proficient in task automation and AI-powered workflow solutions.",
      technologies: JSON.stringify([
        "Python",
        "Selenium",
        "Socialbots",
        "make.com",
        "n8n",
      ]),
      category: "AI/ML",
      order: 2,
    },
    {
      title: "Backend Development",
      description:
        "Experienced in building robust and scalable server-side applications.",
      technologies: JSON.stringify([
        "Django",
        "Flask",
        "FastAPI",
        "Next.js",
        "MERN stack",
      ]),
      category: "Development",
      order: 3,
    },
    {
      title: "Data Scraping and Analysis",
      description:
        "Skilled in extracting, processing and visualizing data for actionable insights.",
      technologies: JSON.stringify([
        "Selenium",
        "Scrapy",
        "BeautifulSoup",
        "Power BI",
        "Streamlit",
        "Pandas",
        "LLMs",
      ]),
      category: "Data Science",
      order: 4,
    },
  ];

  for (const skill of skills) {
    await prisma.skill.create({ data: skill });
  }

  // Seed soft skills
  const softSkills = [
    "Problem Solving",
    "Critical Thinking",
    "Teamwork",
    "Adaptability",
    "Communication",
    "Quick Learning",
    "New Tech Enthusiast",
  ];

  for (let i = 0; i < softSkills.length; i++) {
    await prisma.softSkill.create({
      data: {
        name: softSkills[i],
        order: i + 1,
      },
    });
  }

  // Seed education
  const education = [
    {
      degree: "B.S in Software Engineering",
      institution: "FAST National University of Computer and Emerging Sciences",
      dateRange: " 20 Aug 2022 – Current ",
      location: "City: Chiniot-Faisalabad",
      country: "Country: Pakistan",
      eqfLevel: "Level in EQF: EQF level 4",
      link: "https://cfd.nu.edu.pk",
      logo: "/assets/images/logos/fast-nuces.png",
      current: true,
      order: 1,
    },
    {
      degree: "Intermediate in Computer Science",
      institution: "Superior Group of Colleges",
      dateRange: " 20 Sep 2020 – 10 Aug 2022 ",
      location: "City: Nankana Sahib",
      country: "Country: Pakistan",
      eqfLevel: "Level in EQF: EQF level 4",
      link: "https://superiorcolleges.edu.pk",
      logo: "/assets/images/logos/superior-college.png",
      current: false,
      order: 2,
    },
    {
      degree: "Matric in Science",
      institution: "Center of Excellence Higher Secondary School",
      dateRange: " 8 Aug 2018 – 1 Aug 2020 ",
      location: "City: Jaranwala",
      country: "Country: Pakistan",
      eqfLevel: "Level in EQF: EQF level 4",
      link: "",
      logo: "/assets/images/logos/coe-gov.png",
      current: false,
      order: 3,
    },
  ];

  for (const edu of education) {
    await prisma.education.create({ data: edu });
  }

  console.log("Database seeded successfully!");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
