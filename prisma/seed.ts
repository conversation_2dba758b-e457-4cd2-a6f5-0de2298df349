import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  // Clear existing data
  await prisma.project.deleteMany()
  await prisma.skill.deleteMany()
  await prisma.softSkill.deleteMany()
  await prisma.education.deleteMany()
  await prisma.user.deleteMany()

  // Create admin user
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Admin',
      role: 'admin',
    },
  })

  // Seed projects from existing data
  const projects = [
    {
      name: "LLM Annotator",
      description: "A comprehensive tool for annotating and managing datasets for Large Language Model training with advanced labeling capabilities.",
      content: JSON.stringify([
        "Advanced Dataset Management: Organize and categorize training datasets with intelligent tagging",
        "Multi-format Support: Handle text, JSON, CSV, and custom data formats seamlessly",
        "Collaborative Annotation: Team-based labeling with role management and progress tracking",
        "Quality Assurance: Built-in validation tools and inter-annotator agreement metrics",
        "Export Integration: Direct integration with popular ML frameworks and cloud platforms",
        "Real-time Analytics: Monitor annotation progress and quality metrics in real-time",
        "Scalable Architecture: Handle large datasets with efficient processing and storage"
      ]),
      images: JSON.stringify([
        "/assets/images/projects/llm_annotator_1.webp",
        "/assets/images/projects/llm_annotator_2.webp",
      ]),
      videos: JSON.stringify([]),
      technologies: JSON.stringify([
        "python", "streamlit", "pandas", "sqlite", "plotly", "git", "vscode"
      ]),
      category: "AI and Machine Learning",
      link: "https://github.com/Anas-Altaf/LLM-Annotator",
      featured: true,
      order: 1
    },
    {
      name: "Neurl-PS Scrapper",
      description: "A robust scrapper for https://nips.papers.cc , it could be helpful if you are a researcher or training your LLMs.",
      content: JSON.stringify([
        "Automated Paper Extraction: Scrape research papers from NeurIPS proceedings efficiently",
        "Metadata Collection: Extract titles, authors, abstracts, and publication details",
        "PDF Download Management: Automated downloading with retry mechanisms and error handling",
        "Data Structuring: Organize scraped data into structured formats (JSON, CSV, Database)",
        "Research Analytics: Generate insights and statistics about research trends and topics",
        "Customizable Filters: Filter papers by year, topic, author, or keywords",
        "Export Options: Multiple export formats for integration with research tools"
      ]),
      images: JSON.stringify([
        "/assets/images/projects/nips_ss_1.webp",
        "/assets/images/projects/nips_ss_2.webp",
      ]),
      videos: JSON.stringify(["/assets/videos/projects/nips_vid.mp4"]),
      technologies: JSON.stringify([
        "python", "jupyter", "selenium", "pandas", "streamlit", "git", "pycharm"
      ]),
      category: "Data Scraping and Analysis",
      link: "https://github.com/Anas-Altaf/Neurl-PS-Scrapper",
      featured: true,
      order: 2
    },
    {
      name: "Railway Management System",
      description: "A comprehensive desktop application for managing railway operations, built with C# and SQL Server for efficient train and passenger management.",
      content: JSON.stringify([
        "Train Management: Add, update, and delete train schedules with real-time tracking",
        "Reservation System: Complete booking system with seat selection and payment processing",
        "User Management: Role-based access control for passengers, staff, and administrators",
        "Database Integration: Robust SQL Server integration for data persistence and reporting",
        "Reporting Module: Generate comprehensive reports for operations and financial analysis",
        "Security Features: Encrypted user authentication and secure data handling",
        "Intuitive Interface: User-friendly C# application with responsive design patterns"
      ]),
      images: JSON.stringify([]),
      videos: JSON.stringify([]),
      technologies: JSON.stringify([
        "csharp", "sqlserver", "dotnet", "winforms", "git", "visualstudio"
      ]),
      category: "Desktop Application Development",
      link: "https://github.com/Anas-Altaf/Railway-Management-System",
      featured: false,
      order: 3
    }
  ]

  for (const project of projects) {
    await prisma.project.create({ data: project })
  }

  // Seed skills
  const skills = [
    {
      title: "Frontend Development",
      description: "Skilled in creating dynamic UIs across web, mobile and desktop platforms.",
      technologies: JSON.stringify([
        "React", "Flutter", "Streamlit", "Swing", "Gradio", "Next.js", "Winforms"
      ]),
      category: "Development",
      order: 1
    },
    {
      title: "AI and Automation",
      description: "Proficient in task automation and AI-powered workflow solutions.",
      technologies: JSON.stringify([
        "Python", "Selenium", "Socialbots", "make.com", "n8n"
      ]),
      category: "AI/ML",
      order: 2
    },
    {
      title: "Backend Development",
      description: "Experienced in building robust and scalable server-side applications.",
      technologies: JSON.stringify([
        "Django", "Flask", "FastAPI", "Next.js", "MERN stack"
      ]),
      category: "Development",
      order: 3
    },
    {
      title: "Data Scraping and Analysis",
      description: "Skilled in extracting, processing and visualizing data for actionable insights.",
      technologies: JSON.stringify([
        "Selenium", "Scrapy", "BeautifulSoup", "Power BI", "Streamlit", "Pandas", "LLMs"
      ]),
      category: "Data Science",
      order: 4
    }
  ]

  for (const skill of skills) {
    await prisma.skill.create({ data: skill })
  }

  // Seed soft skills
  const softSkills = [
    "Problem Solving",
    "Critical Thinking", 
    "Teamwork",
    "Adaptability",
    "Communication",
    "Quick Learning",
    "New Tech Enthusiast"
  ]

  for (let i = 0; i < softSkills.length; i++) {
    await prisma.softSkill.create({
      data: {
        name: softSkills[i],
        order: i + 1
      }
    })
  }

  // Seed education
  const education = [
    {
      degree: "B.S in Software Engineering",
      institution: "FAST National University of Computer and Emerging Sciences",
      dateRange: " 20 Aug 2022 – Current ",
      location: "City: Chiniot-Faisalabad",
      country: "Country: Pakistan",
      eqfLevel: "Level in EQF: EQF level 4",
      link: "https://cfd.nu.edu.pk",
      logo: "/assets/images/logos/fast-nuces.png",
      current: true,
      order: 1
    },
    {
      degree: "Intermediate in Computer Science",
      institution: "Superior Group of Colleges",
      dateRange: " 20 Sep 2020 – 10 Aug 2022 ",
      location: "City: Nankana Sahib",
      country: "Country: Pakistan",
      eqfLevel: "Level in EQF: EQF level 4",
      link: "https://superiorcolleges.edu.pk",
      logo: "/assets/images/logos/superior-college.png",
      current: false,
      order: 2
    },
    {
      degree: "Matric in Science",
      institution: "Center of Excellence Higher Secondary School",
      dateRange: " 8 Aug 2018 – 1 Aug 2020 ",
      location: "City: Jaranwala",
      country: "Country: Pakistan",
      eqfLevel: "Level in EQF: EQF level 4",
      link: "",
      logo: "/assets/images/logos/coe-gov.png",
      current: false,
      order: 3
    }
  ]

  for (const edu of education) {
    await prisma.education.create({ data: edu })
  }

  console.log('Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
