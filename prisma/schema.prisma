// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String
  content     String   // JSON string for rich content blocks
  images      String   // JSON array of image URLs
  videos      String   // JSON array of video URLs  
  technologies String  // JSON array of tech stack
  category    String
  link        String?
  featured    <PERSON>olean  @default(false)
  published   Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("projects")
}

model Skill {
  id          String @id @default(cuid())
  title       String
  description String
  technologies String // JSON array of technologies
  category    String
  order       Int    @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("skills")
}

model SoftSkill {
  id        String   @id @default(cuid())
  name      String   @unique
  order     Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("soft_skills")
}

model Education {
  id          String   @id @default(cuid())
  degree      String
  institution String
  dateRange   String
  location    String
  country     String
  eqfLevel    String?
  logo        String?
  link        String?
  current     Boolean  @default(false)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("education")
}

model Media {
  id        String   @id @default(cuid())
  filename  String
  originalName String
  mimeType  String
  size      Int
  url       String
  alt       String?
  category  String   // 'image', 'video', 'document'
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("media")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String?
  role      String   @default("admin") // 'admin', 'editor', 'viewer'
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("users")
}

model SiteSettings {
  id              Int      @id @default(1)
  siteName        String
  siteDescription String
  heroTitle       String
  heroSubtitle    String
  contactEmail    String
  heroImage       String?
  logoImage       String?
  footerImage     String?
  favicon         String?
  githubUrl       String?
  linkedinUrl     String?
  mediumUrl       String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("site_settings")
}
