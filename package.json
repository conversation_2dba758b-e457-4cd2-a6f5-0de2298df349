{"name": "portfolio-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "verify": "node scripts/verify-setup.js", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.7", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.7", "@tiptap/extension-image": "^2.12.2", "@tiptap/extension-link": "^2.12.2", "@tiptap/pm": "^2.12.2", "@tiptap/react": "^2.12.2", "@tiptap/starter-kit": "^2.12.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^4.1.0", "framer-motion": "^12.15.0", "gh-pages": "^6.3.0", "lucide-react": "^0.511.0", "marked": "^15.0.12", "multer": "^1.4.5-lts.1", "next": "15.3.3", "next-auth": "^4.24.10", "react": "^19.0.0", "react-day-picker": "^9.4.2", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "react-ga4": "^2.1.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-slick": "^0.30.3", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "sharp": "^0.33.5", "slick-carousel": "^1.8.1", "sonner": "^1.7.3", "tailwind-merge": "^3.3.0", "vaul": "^1.1.1", "zod": "^3.25.42"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/multer": "^1.4.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-slick": "^0.23.13", "eslint": "^9", "eslint-config-next": "15.3.3", "prisma": "^6.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.2", "typescript": "^5"}}