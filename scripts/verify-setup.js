#!/usr/bin/env node

/**
 * Verification script to check if all optimizations are properly implemented
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Portfolio Optimizations...\n');

const checks = [
  {
    name: 'Error Boundary Components',
    check: () => fs.existsSync('src/components/error/ErrorBoundary.tsx'),
    description: 'Error boundary component exists'
  },
  {
    name: 'Loading Components',
    check: () => fs.existsSync('src/components/loading/SkeletonLoaders.tsx'),
    description: 'Skeleton loader components exist'
  },
  {
    name: 'SEO Components',
    check: () => fs.existsSync('src/components/seo/SEOHead.tsx'),
    description: 'SEO utility components exist'
  },
  {
    name: 'Performance Monitoring',
    check: () => fs.existsSync('src/hooks/usePerformanceMonitor.ts'),
    description: 'Performance monitoring hook exists'
  },
  {
    name: 'Global Error Page',
    check: () => fs.existsSync('src/app/error.tsx'),
    description: 'Global error page exists'
  },
  {
    name: 'Loading Page',
    check: () => fs.existsSync('src/app/loading.tsx'),
    description: 'Global loading page exists'
  },
  {
    name: 'Not Found Page',
    check: () => fs.existsSync('src/app/not-found.tsx'),
    description: 'Custom 404 page exists'
  },
  {
    name: 'Sitemap',
    check: () => fs.existsSync('src/app/sitemap.ts'),
    description: 'SEO sitemap exists'
  },
  {
    name: 'Robots.txt',
    check: () => fs.existsSync('src/app/robots.ts'),
    description: 'Robots.txt configuration exists'
  },
  {
    name: 'PWA Manifest',
    check: () => fs.existsSync('src/app/manifest.ts'),
    description: 'PWA manifest exists'
  },
  {
    name: 'Next.js Config Optimization',
    check: () => {
      const config = fs.readFileSync('next.config.mjs', 'utf8');
      return config.includes('optimizeCss') && config.includes('optimizePackageImports');
    },
    description: 'Next.js config includes performance optimizations'
  },
  {
    name: 'Dynamic Imports in PortfolioClient',
    check: () => {
      const client = fs.readFileSync('src/components/PortfolioClient.tsx', 'utf8');
      return client.includes('dynamic(') && client.includes('Suspense');
    },
    description: 'PortfolioClient uses dynamic imports and Suspense'
  }
];

let passed = 0;
let failed = 0;

checks.forEach(({ name, check, description }) => {
  try {
    const result = check();
    if (result) {
      console.log(`✅ ${name}: ${description}`);
      passed++;
    } else {
      console.log(`❌ ${name}: ${description}`);
      failed++;
    }
  } catch (error) {
    console.log(`❌ ${name}: Error checking - ${error.message}`);
    failed++;
  }
});

console.log(`\n📊 Results: ${passed} passed, ${failed} failed`);

if (failed === 0) {
  console.log('\n🎉 All optimizations are properly implemented!');
  console.log('\n📋 Next steps:');
  console.log('1. Run `npm run dev` to start development server');
  console.log('2. Test the application in browser');
  console.log('3. Check browser DevTools for performance metrics');
  console.log('4. Verify error boundaries by triggering errors');
  console.log('5. Test loading states and Suspense boundaries');
} else {
  console.log('\n⚠️  Some optimizations are missing. Please check the failed items above.');
  process.exit(1);
}
