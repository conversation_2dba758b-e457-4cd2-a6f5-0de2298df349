# Anas Altaf - Portfolio Website

A modern, high-performance portfolio website built with Next.js 15, TypeScript, Tailwind CSS, and Framer Motion. Features comprehensive performance optimization, SEO enhancements, and robust error handling.

## 🚀 Features

### Performance Optimizations

- **Dynamic Imports**: Code splitting for below-the-fold components
- **React Suspense**: Lazy loading with skeleton screens
- **Image Optimization**: Next.js Image component with WebP/AVIF support
- **Bundle Optimization**: Tree shaking and package optimization
- **Performance Monitoring**: Real-time Web Vitals tracking
- **Caching**: Optimized headers and static asset caching

### SEO Enhancements

- **Structured Data**: JSON-LD for Person and Website schemas
- **Meta Tags**: Comprehensive Open Graph and Twitter Card support
- **Sitemap**: Auto-generated XML sitemap
- **Robots.txt**: Search engine crawling directives
- **PWA Manifest**: Progressive Web App capabilities
- **Semantic HTML**: Proper heading hierarchy and ARIA labels

### Error Handling & Reliability

- **Error Boundaries**: Component-level error isolation
- **Global Error Pages**: Custom 404 and error pages
- **Fallback Components**: Graceful degradation for failed components
- **Loading States**: Skeleton screens and spinners
- **Retry Mechanisms**: User-friendly error recovery

### User Experience

- **Responsive Design**: Mobile-first approach
- **Smooth Animations**: Framer Motion with performance optimization
- **Accessibility**: WCAG 2.1 AA compliance
- **Dark Theme**: Optimized for dark mode
- **Fast Loading**: Optimized fonts and resources

## 🛠️ Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Icons**: Lucide React + FontAwesome
- **Analytics**: Google Analytics 4
- **Deployment**: GitHub Pages

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── globals.css        # Global styles and theme
│   ├── layout.tsx         # Root layout with SEO
│   ├── page.tsx           # Home page
│   ├── loading.tsx        # Global loading UI
│   ├── error.tsx          # Global error UI
│   ├── not-found.tsx      # 404 page
│   ├── sitemap.ts         # SEO sitemap
│   ├── robots.ts          # Robots.txt
│   └── manifest.ts        # PWA manifest
├── components/            # Reusable components
│   ├── error/            # Error boundaries
│   ├── loading/          # Loading components
│   ├── seo/              # SEO utilities
│   ├── monitoring/       # Performance monitoring
│   ├── header/           # Hero and navigation
│   ├── projects/         # Project showcase
│   ├── skills/           # Skills section
│   ├── education/        # Education timeline
│   ├── blogs/            # Blog integration
│   ├── contact/          # Contact form
│   └── ui/               # Base UI components
└── hooks/                # Custom React hooks
    └── usePerformanceMonitor.ts
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, pnpm, or bun

### Installation

1. Clone the repository:

```bash
git clone https://github.com/Anas-Altaf/portfolio-next-js.git
cd portfolio-next-js
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📊 Performance Metrics

The website is optimized for Core Web Vitals:

- **Largest Contentful Paint (LCP)**: < 2.5s
- **First Input Delay (FID)**: < 100ms
- **Cumulative Layout Shift (CLS)**: < 0.1
- **First Contentful Paint (FCP)**: < 1.8s
- **Time to First Byte (TTFB)**: < 600ms

### Performance Features

- Dynamic imports for code splitting
- Image optimization with next/image
- Font optimization with next/font
- Preconnect to external domains
- Optimized bundle size
- Real-time performance monitoring

## 🔍 SEO Features

### Structured Data

- Person schema for professional information
- Website schema for site metadata
- Breadcrumb navigation
- Article schemas for blog posts

### Meta Tags

- Open Graph for social sharing
- Twitter Cards for Twitter sharing
- Canonical URLs
- Language declarations
- Viewport optimization

### Technical SEO

- XML sitemap generation
- Robots.txt configuration
- Proper heading hierarchy
- Alt text for images
- Semantic HTML structure

## 🛡️ Error Handling

### Error Boundaries

- Component-level error isolation
- Graceful fallback UIs
- Error logging and reporting
- Development error details
- User-friendly error messages

### Loading States

- Skeleton screens for better UX
- Progressive loading
- Suspense boundaries
- Retry mechanisms
- Offline support

## 🎨 Customization

### Theme Configuration

Update colors in `src/app/globals.css`:

```css
@theme {
  --color-acc: var(--color-green-400);     # Accent color
  --color-bg-p: #181818;                   # Primary background
  --color-fg-p: #282828;                   # Primary foreground
  --color-fg-s: #424242;                   # Secondary foreground
  --color-txt-desc: #B3B3B3;              # Description text
}
```

### SEO Configuration

Update SEO settings in `src/components/seo/SEOHead.tsx`:

- Personal information
- Social media links
- Contact details
- Professional details

## 📈 Analytics & Monitoring

### Google Analytics 4

- Page views tracking
- User engagement metrics
- Scroll depth tracking
- Custom events
- Performance metrics

### Performance Monitoring

- Web Vitals tracking
- Long task detection
- Memory usage monitoring
- Error tracking
- Custom metrics

## 🚀 Deployment

### GitHub Pages

1. Update the base URL in SEO configuration
2. Build the project:

```bash
npm run build
```

3. Deploy to GitHub Pages:

```bash
npm run deploy
```

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically on push

## 🔧 Development

### Code Quality

- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting
- Husky for git hooks

### Testing (Recommended additions)

```bash
# Add testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
```

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions, issues, and feature requests are welcome! Feel free to check the [issues page](https://github.com/Anas-Altaf/portfolio-next-js/issues).

## 📞 Contact

- **Website**: [https://anas-altaf.github.io](https://anas-altaf.github.io)
- **GitHub**: [@Anas-Altaf](https://github.com/Anas-Altaf)
- **LinkedIn**: [Anas Altaf](https://linkedin.com/in/anas-altaf)
- **Medium**: [@anasaltaf](https://medium.com/@anasaltaf)

---

⭐ Star this repository if you found it helpful!
# Portfolio-Frontend
