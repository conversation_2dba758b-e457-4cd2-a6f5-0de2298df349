"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { 
  FolderOpen, 
  GraduationCap, 
  Lightbulb, 
  Image, 
  Plus,
  TrendingUp,
  Users,
  Eye
} from "lucide-react"
import Link from "next/link"

interface DashboardStats {
  projects: number
  skills: number
  education: number
  media: number
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    projects: 0,
    skills: 0,
    education: 0,
    media: 0,
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/admin/stats")
      if (response.ok) {
        const data = await response.json()
        setStats(data)
      }
    } catch (error) {
      console.error("Failed to fetch stats:", error)
    } finally {
      setLoading(false)
    }
  }

  const quickActions = [
    {
      title: "Add New Project",
      description: "Create a new portfolio project",
      href: "/admin/projects/new",
      icon: FolderOpen,
      color: "bg-blue-500",
    },
    {
      title: "Add Skill",
      description: "Add a new skill to your portfolio",
      href: "/admin/skills/new",
      icon: Lightbulb,
      color: "bg-green-500",
    },
    {
      title: "Add Education",
      description: "Add educational background",
      href: "/admin/education/new",
      icon: GraduationCap,
      color: "bg-purple-500",
    },
    {
      title: "Upload Media",
      description: "Upload images and videos",
      href: "/admin/media",
      icon: Image,
      color: "bg-orange-500",
    },
  ]

  const statCards = [
    {
      title: "Total Projects",
      value: stats.projects,
      icon: FolderOpen,
      href: "/admin/projects",
      color: "text-blue-600",
    },
    {
      title: "Skills",
      value: stats.skills,
      icon: Lightbulb,
      href: "/admin/skills",
      color: "text-green-600",
    },
    {
      title: "Education Entries",
      value: stats.education,
      icon: GraduationCap,
      href: "/admin/education",
      color: "text-purple-600",
    },
    {
      title: "Media Files",
      value: stats.media,
      icon: Image,
      href: "/admin/media",
      color: "text-orange-600",
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome back! Here's an overview of your portfolio content.
          </p>
        </div>
        <Button asChild>
          <Link href="/" target="_blank">
            <Eye className="mr-2 h-4 w-4" />
            View Live Site
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <Link key={stat.title} href={stat.href}>
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {stat.title}
                </CardTitle>
                <stat.icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? "..." : stat.value}
                </div>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => (
            <Link key={action.title} href={action.href}>
              <Card className="hover:shadow-md transition-shadow cursor-pointer">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color} text-white`}>
                      <action.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-sm">{action.title}</CardTitle>
                      <CardDescription className="text-xs">
                        {action.description}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
              </Card>
            </Link>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Latest updates to your portfolio content
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm font-medium">Portfolio initialized</p>
                <p className="text-xs text-gray-500">Database and admin panel set up</p>
              </div>
              <Badge variant="secondary">Just now</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
