"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, MoreHorizontal, Edit, Trash2, ExternalLink, Star } from "lucide-react"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"
import { toast } from "sonner"

interface Education {
  id: string
  degree: string
  institution: string
  dateRange: string
  location: string
  country: string
  eqfLevel?: string
  logo?: string
  link?: string
  current: boolean
  order: number
  createdAt: string
  updatedAt: string
}

export default function EducationPage() {
  const [education, setEducation] = useState<Education[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchEducation()
  }, [])

  const fetchEducation = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch("/api/admin/education")
      if (!response.ok) {
        throw new Error("Failed to fetch education")
      }
      const data = await response.json()
      setEducation(data)
    } catch (error) {
      setError("Failed to load education")
      console.error("Error fetching education:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this education entry?")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/education/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete education")
      }

      setEducation(education.filter(e => e.id !== id))
      toast.success("Education entry deleted successfully")
    } catch (error) {
      toast.error("Failed to delete education entry")
      console.error("Error deleting education:", error)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading education..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchEducation} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Education</h1>
          <p className="text-gray-600 mt-1">
            Manage your educational background
          </p>
        </div>
        <Button asChild>
          <Link href="/admin/education/new">
            <Plus className="mr-2 h-4 w-4" />
            Add Education
          </Link>
        </Button>
      </div>

      {education.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No education entries yet</CardTitle>
            <CardDescription>
              Get started by adding your first education entry
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/admin/education/new">
                <Plus className="mr-2 h-4 w-4" />
                Add Education
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Institution</TableHead>
                <TableHead>Degree</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {education.map((edu) => (
                <TableRow key={edu.id}>
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      {edu.logo && (
                        <div className="w-8 h-8 relative">
                          <Image
                            src={edu.logo}
                            alt={`${edu.institution} logo`}
                            fill
                            className="object-contain rounded"
                          />
                        </div>
                      )}
                      <div>
                        <div className="font-medium">{edu.institution}</div>
                        {edu.link && (
                          <a
                            href={edu.link}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-sm text-blue-600 hover:underline flex items-center gap-1"
                          >
                            Visit website
                            <ExternalLink className="h-3 w-3" />
                          </a>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{edu.degree}</div>
                      {edu.eqfLevel && (
                        <div className="text-sm text-gray-500">{edu.eqfLevel}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm">{edu.dateRange}</span>
                      {edu.current && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      <div>{edu.location}</div>
                      <div className="text-gray-500">{edu.country}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={edu.current ? "default" : "secondary"}>
                      {edu.current ? "Current" : "Completed"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/admin/education/${edu.id}`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(edu.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </Card>
      )}
    </div>
  )
}
