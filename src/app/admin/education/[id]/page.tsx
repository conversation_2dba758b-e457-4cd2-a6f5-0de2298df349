"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import EducationForm from "@/components/admin/EducationForm"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"

export default function EditEducationPage() {
  const params = useParams()
  const educationId = params.id as string
  const [education, setEducation] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchEducation()
  }, [educationId])

  const fetchEducation = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/admin/education/${educationId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Education entry not found")
        }
        throw new Error("Failed to fetch education")
      }
      
      const data = await response.json()
      setEducation(data)
    } catch (error: any) {
      setError(error.message)
      console.error("Error fetching education:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading education..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchEducation} />
  }

  return <EducationForm educationId={educationId} initialData={education} />
}
