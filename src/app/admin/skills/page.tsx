"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger } from "@/components/ui/tabs"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, MoreHorizontal, Edit, Trash2, Lightbulb, Brain } from "lucide-react"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"
import { toast } from "sonner"

interface Skill {
  id: string
  title: string
  description: string
  technologies: string[]
  category: string
  order: number
  createdAt: string
  updatedAt: string
}

interface SoftSkill {
  id: string
  name: string
  order: number
  createdAt: string
  updatedAt: string
}

export default function SkillsPage() {
  const [skills, setSkills] = useState<Skill[]>([])
  const [softSkills, setSoftSkills] = useState<SoftSkill[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const [skillsResponse, softSkillsResponse] = await Promise.all([
        fetch("/api/admin/skills"),
        fetch("/api/admin/soft-skills")
      ])

      if (!skillsResponse.ok || !softSkillsResponse.ok) {
        throw new Error("Failed to fetch skills")
      }

      const [skillsData, softSkillsData] = await Promise.all([
        skillsResponse.json(),
        softSkillsResponse.json()
      ])

      setSkills(skillsData)
      setSoftSkills(softSkillsData)
    } catch (error) {
      setError("Failed to load skills")
      console.error("Error fetching skills:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSkill = async (id: string) => {
    if (!confirm("Are you sure you want to delete this skill?")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/skills/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete skill")
      }

      setSkills(skills.filter(s => s.id !== id))
      toast.success("Skill deleted successfully")
    } catch (error) {
      toast.error("Failed to delete skill")
      console.error("Error deleting skill:", error)
    }
  }

  const handleDeleteSoftSkill = async (id: string) => {
    if (!confirm("Are you sure you want to delete this soft skill?")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/soft-skills/${id}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        throw new Error("Failed to delete soft skill")
      }

      setSoftSkills(softSkills.filter(s => s.id !== id))
      toast.success("Soft skill deleted successfully")
    } catch (error) {
      toast.error("Failed to delete soft skill")
      console.error("Error deleting soft skill:", error)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading skills..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchData} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Skills</h1>
          <p className="text-gray-600 mt-1">
            Manage your technical and soft skills
          </p>
        </div>
      </div>

      <Tabs defaultValue="technical" className="space-y-6">
        <TabsList>
          <TabsTrigger value="technical" className="flex items-center gap-2">
            <Lightbulb className="h-4 w-4" />
            Technical Skills
          </TabsTrigger>
          <TabsTrigger value="soft" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            Soft Skills
          </TabsTrigger>
        </TabsList>

        <TabsContent value="technical" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Technical Skills</h2>
            <Button asChild>
              <Link href="/admin/skills/new">
                <Plus className="mr-2 h-4 w-4" />
                Add Technical Skill
              </Link>
            </Button>
          </div>

          {skills.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>No technical skills yet</CardTitle>
                <CardDescription>
                  Get started by adding your first technical skill
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild>
                  <Link href="/admin/skills/new">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Technical Skill
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Technologies</TableHead>
                    <TableHead>Order</TableHead>
                    <TableHead className="w-[100px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {skills.map((skill) => (
                    <TableRow key={skill.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{skill.title}</div>
                          <div className="text-sm text-gray-500 truncate max-w-[300px]">
                            {skill.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{skill.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {skill.technologies.slice(0, 3).map((tech) => (
                            <Badge key={tech} variant="outline" className="text-xs">
                              {tech}
                            </Badge>
                          ))}
                          {skill.technologies.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{skill.technologies.length - 3}
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{skill.order}</TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/skills/${skill.id}`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDeleteSkill(skill.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="soft" className="space-y-6">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Soft Skills</h2>
            <Button asChild>
              <Link href="/admin/skills/soft/new">
                <Plus className="mr-2 h-4 w-4" />
                Add Soft Skill
              </Link>
            </Button>
          </div>

          {softSkills.length === 0 ? (
            <Card>
              <CardHeader>
                <CardTitle>No soft skills yet</CardTitle>
                <CardDescription>
                  Get started by adding your first soft skill
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild>
                  <Link href="/admin/skills/soft/new">
                    <Plus className="mr-2 h-4 w-4" />
                    Add Soft Skill
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {softSkills.map((softSkill) => (
                <Card key={softSkill.id}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">{softSkill.order}</Badge>
                        <span className="font-medium">{softSkill.name}</span>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/admin/skills/soft/${softSkill.id}`}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDeleteSoftSkill(softSkill.id)}
                            className="text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
