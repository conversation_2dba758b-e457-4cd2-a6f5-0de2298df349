"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import SkillForm from "@/components/admin/SkillForm"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"

export default function EditSkillPage() {
  const params = useParams()
  const skillId = params.id as string
  const [skill, setSkill] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSkill()
  }, [skillId])

  const fetchSkill = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/admin/skills/${skillId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Skill not found")
        }
        throw new Error("Failed to fetch skill")
      }
      
      const data = await response.json()
      setSkill(data)
    } catch (error: any) {
      setError(error.message)
      console.error("Error fetching skill:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading skill..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchSkill} />
  }

  return <SkillForm skillId={skillId} initialData={skill} />
}
