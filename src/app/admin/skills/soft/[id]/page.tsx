"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import SoftSkillForm from "@/components/admin/SoftSkillForm"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"

export default function EditSoftSkillPage() {
  const params = useParams()
  const skillId = params.id as string
  const [skill, setSkill] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchSkill()
  }, [skillId])

  const fetchSkill = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/admin/soft-skills/${skillId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Soft skill not found")
        }
        throw new Error("Failed to fetch soft skill")
      }
      
      const data = await response.json()
      setSkill(data)
    } catch (error: any) {
      setError(error.message)
      console.error("Error fetching soft skill:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading soft skill..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchSkill} />
  }

  return <SoftSkillForm skillId={skillId} initialData={skill} />
}
