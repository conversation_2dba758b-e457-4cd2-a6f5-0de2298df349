"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Save, Eye } from "lucide-react";
import Image from "next/image";
import AdminLoading from "@/components/admin/AdminLoading";
import AdminError from "@/components/admin/AdminError";

interface SettingsFormData {
  siteName: string;
  siteDescription: string;
  heroImage: string;
  logoImage: string;
  footerImage: string;
  favicon: string;
  heroTitle: string;
  heroSubtitle: string;
  contactEmail: string;
  githubUrl: string;
  linkedinUrl: string;
  mediumUrl: string;
}

export default function SettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<SettingsFormData>({
    siteName: "Anas Altaf Portfolio",
    siteDescription: "Software Engineer & AI Enthusiast",
    heroTitle: "Hi, I'm Anas Altaf",
    heroSubtitle: "Software Engineer & AI Enthusiast",
    contactEmail: "<EMAIL>",
    heroImage: "/assets/images/personal-img-art.webp",
    logoImage: "/assets/images/personal-img-art.webp",
    footerImage: "/assets/images/personal-img-art.webp",
    favicon: "/favicon.ico",
    githubUrl: "https://github.com/Anas-Altaf",
    linkedinUrl: "https://linkedin.com/in/anas-altaf",
    mediumUrl: "https://medium.com/@anas-altaf",
  });

  const handleInputChange = (field: keyof SettingsFormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await fetch("/api/admin/settings");
      if (response.ok) {
        const data = await response.json();
        setFormData(data);
      }
    } catch (error) {
      setError("Failed to load settings");
      console.error("Error fetching settings:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    try {
      const response = await fetch("/api/admin/settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error("Failed to save settings");
      }

      alert("Settings saved successfully!");
    } catch (error) {
      alert("Failed to save settings");
      console.error("Error saving settings:", error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return <AdminLoading message="Loading settings..." />;
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchSettings} />;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Site Settings</h1>
          <p className="text-gray-600 mt-1">
            Customize your portfolio appearance and content
          </p>
        </div>
        <Button onClick={() => window.open("/", "_blank")} variant="outline">
          <Eye className="mr-2 h-4 w-4" />
          Preview Site
        </Button>
      </div>

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="general" className="space-y-6">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="images">Images & Media</TabsTrigger>
            <TabsTrigger value="social">Social Links</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Site Information</CardTitle>
                <CardDescription>
                  Basic information about your portfolio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input
                    id="siteName"
                    value={formData.siteName}
                    onChange={(e) =>
                      handleInputChange("siteName", e.target.value)
                    }
                    placeholder="Your Portfolio Name"
                  />
                </div>

                <div>
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    value={formData.siteDescription}
                    onChange={(e) =>
                      handleInputChange("siteDescription", e.target.value)
                    }
                    placeholder="Brief description of your portfolio"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="heroTitle">Hero Section Title</Label>
                  <Input
                    id="heroTitle"
                    value={formData.heroTitle}
                    onChange={(e) =>
                      handleInputChange("heroTitle", e.target.value)
                    }
                    placeholder="Main title on homepage"
                  />
                </div>

                <div>
                  <Label htmlFor="heroSubtitle">Hero Section Subtitle</Label>
                  <Input
                    id="heroSubtitle"
                    {...register("heroSubtitle")}
                    placeholder="Subtitle or tagline"
                  />
                  {errors.heroSubtitle && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.heroSubtitle.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    {...register("contactEmail")}
                    placeholder="<EMAIL>"
                  />
                  {errors.contactEmail && (
                    <p className="text-sm text-red-600 mt-1">
                      {errors.contactEmail.message}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="images" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Images & Media</CardTitle>
                <CardDescription>
                  Customize images used throughout your portfolio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="heroImage">Hero Section Image</Label>
                    <Input
                      id="heroImage"
                      {...register("heroImage")}
                      placeholder="/assets/images/hero/hero-image.jpg"
                    />
                    {watch("heroImage") && (
                      <div className="mt-2 relative h-32 w-full rounded-lg overflow-hidden">
                        <Image
                          src={watch("heroImage") || ""}
                          alt="Hero preview"
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="logoImage">Logo Image</Label>
                    <Input
                      id="logoImage"
                      {...register("logoImage")}
                      placeholder="/assets/images/logo/logo.png"
                    />
                    {watch("logoImage") && (
                      <div className="mt-2 relative h-16 w-32 rounded-lg overflow-hidden">
                        <Image
                          src={watch("logoImage") || ""}
                          alt="Logo preview"
                          fill
                          className="object-contain"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="footerImage">Footer Background Image</Label>
                    <Input
                      id="footerImage"
                      {...register("footerImage")}
                      placeholder="/assets/images/footer/footer-bg.jpg"
                    />
                    {watch("footerImage") && (
                      <div className="mt-2 relative h-24 w-full rounded-lg overflow-hidden">
                        <Image
                          src={watch("footerImage") || ""}
                          alt="Footer preview"
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="favicon">Favicon</Label>
                    <Input
                      id="favicon"
                      {...register("favicon")}
                      placeholder="/favicon.ico"
                    />
                    {watch("favicon") && (
                      <div className="mt-2 relative h-8 w-8 rounded overflow-hidden">
                        <Image
                          src={watch("favicon") || ""}
                          alt="Favicon preview"
                          fill
                          className="object-contain"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="social" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Social Media Links</CardTitle>
                <CardDescription>
                  Add your social media profiles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="githubUrl">GitHub URL</Label>
                  <Input
                    id="githubUrl"
                    {...register("githubUrl")}
                    placeholder="https://github.com/username"
                  />
                </div>

                <div>
                  <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
                  <Input
                    id="linkedinUrl"
                    {...register("linkedinUrl")}
                    placeholder="https://linkedin.com/in/username"
                  />
                </div>

                <div>
                  <Label htmlFor="mediumUrl">Medium URL</Label>
                  <Input
                    id="mediumUrl"
                    {...register("mediumUrl")}
                    placeholder="https://medium.com/@username"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button type="submit" disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </form>
    </div>
  );
}
