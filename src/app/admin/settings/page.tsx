"use client"

import { useState, useEffect } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Save, Upload, Eye } from "lucide-react"
import { toast } from "sonner"
import Image from "next/image"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"

const settingsSchema = z.object({
  siteName: z.string().min(1, "Site name is required"),
  siteDescription: z.string().min(1, "Site description is required"),
  heroImage: z.string().url().optional().or(z.literal("")),
  logoImage: z.string().url().optional().or(z.literal("")),
  footerImage: z.string().url().optional().or(z.literal("")),
  favicon: z.string().url().optional().or(z.literal("")),
  heroTitle: z.string().min(1, "Hero title is required"),
  heroSubtitle: z.string().min(1, "Hero subtitle is required"),
  contactEmail: z.string().email("Valid email is required"),
  githubUrl: z.string().url().optional().or(z.literal("")),
  linkedinUrl: z.string().url().optional().or(z.literal("")),
  mediumUrl: z.string().url().optional().or(z.literal("")),
})

type SettingsFormData = z.infer<typeof settingsSchema>

export default function SettingsPage() {
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      siteName: "Anas Altaf Portfolio",
      siteDescription: "Software Engineer & AI Enthusiast",
      heroTitle: "Hi, I'm Anas Altaf",
      heroSubtitle: "Software Engineer & AI Enthusiast",
      contactEmail: "<EMAIL>",
      heroImage: "/assets/images/hero/hero-image.jpg",
      logoImage: "/assets/images/logo/logo.png",
      footerImage: "/assets/images/footer/footer-bg.jpg",
      favicon: "/favicon.ico",
      githubUrl: "https://github.com/Anas-Altaf",
      linkedinUrl: "https://linkedin.com/in/anas-altaf",
      mediumUrl: "https://medium.com/@anas-altaf",
    },
  })

  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch("/api/admin/settings")
      if (response.ok) {
        const data = await response.json()
        reset(data)
      }
    } catch (error) {
      setError("Failed to load settings")
      console.error("Error fetching settings:", error)
    } finally {
      setLoading(false)
    }
  }

  const onSubmit = async (data: SettingsFormData) => {
    setSaving(true)
    try {
      const response = await fetch("/api/admin/settings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error("Failed to save settings")
      }

      toast.success("Settings saved successfully")
    } catch (error) {
      toast.error("Failed to save settings")
      console.error("Error saving settings:", error)
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading settings..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchSettings} />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Site Settings</h1>
          <p className="text-gray-600 mt-1">
            Customize your portfolio appearance and content
          </p>
        </div>
        <Button onClick={() => window.open("/", "_blank")} variant="outline">
          <Eye className="mr-2 h-4 w-4" />
          Preview Site
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)}>
        <Tabs defaultValue="general" className="space-y-6">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="images">Images & Media</TabsTrigger>
            <TabsTrigger value="social">Social Links</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Site Information</CardTitle>
                <CardDescription>
                  Basic information about your portfolio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input
                    id="siteName"
                    {...register("siteName")}
                    placeholder="Your Portfolio Name"
                  />
                  {errors.siteName && (
                    <p className="text-sm text-red-600 mt-1">{errors.siteName.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="siteDescription">Site Description</Label>
                  <Textarea
                    id="siteDescription"
                    {...register("siteDescription")}
                    placeholder="Brief description of your portfolio"
                    rows={3}
                  />
                  {errors.siteDescription && (
                    <p className="text-sm text-red-600 mt-1">{errors.siteDescription.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="heroTitle">Hero Section Title</Label>
                  <Input
                    id="heroTitle"
                    {...register("heroTitle")}
                    placeholder="Main title on homepage"
                  />
                  {errors.heroTitle && (
                    <p className="text-sm text-red-600 mt-1">{errors.heroTitle.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="heroSubtitle">Hero Section Subtitle</Label>
                  <Input
                    id="heroSubtitle"
                    {...register("heroSubtitle")}
                    placeholder="Subtitle or tagline"
                  />
                  {errors.heroSubtitle && (
                    <p className="text-sm text-red-600 mt-1">{errors.heroSubtitle.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="contactEmail">Contact Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    {...register("contactEmail")}
                    placeholder="<EMAIL>"
                  />
                  {errors.contactEmail && (
                    <p className="text-sm text-red-600 mt-1">{errors.contactEmail.message}</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="images" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Images & Media</CardTitle>
                <CardDescription>
                  Customize images used throughout your portfolio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="heroImage">Hero Section Image</Label>
                    <Input
                      id="heroImage"
                      {...register("heroImage")}
                      placeholder="/assets/images/hero/hero-image.jpg"
                    />
                    {watch("heroImage") && (
                      <div className="mt-2 relative h-32 w-full rounded-lg overflow-hidden">
                        <Image
                          src={watch("heroImage") || ""}
                          alt="Hero preview"
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="logoImage">Logo Image</Label>
                    <Input
                      id="logoImage"
                      {...register("logoImage")}
                      placeholder="/assets/images/logo/logo.png"
                    />
                    {watch("logoImage") && (
                      <div className="mt-2 relative h-16 w-32 rounded-lg overflow-hidden">
                        <Image
                          src={watch("logoImage") || ""}
                          alt="Logo preview"
                          fill
                          className="object-contain"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="footerImage">Footer Background Image</Label>
                    <Input
                      id="footerImage"
                      {...register("footerImage")}
                      placeholder="/assets/images/footer/footer-bg.jpg"
                    />
                    {watch("footerImage") && (
                      <div className="mt-2 relative h-24 w-full rounded-lg overflow-hidden">
                        <Image
                          src={watch("footerImage") || ""}
                          alt="Footer preview"
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="favicon">Favicon</Label>
                    <Input
                      id="favicon"
                      {...register("favicon")}
                      placeholder="/favicon.ico"
                    />
                    {watch("favicon") && (
                      <div className="mt-2 relative h-8 w-8 rounded overflow-hidden">
                        <Image
                          src={watch("favicon") || ""}
                          alt="Favicon preview"
                          fill
                          className="object-contain"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="social" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Social Media Links</CardTitle>
                <CardDescription>
                  Add your social media profiles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="githubUrl">GitHub URL</Label>
                  <Input
                    id="githubUrl"
                    {...register("githubUrl")}
                    placeholder="https://github.com/username"
                  />
                </div>

                <div>
                  <Label htmlFor="linkedinUrl">LinkedIn URL</Label>
                  <Input
                    id="linkedinUrl"
                    {...register("linkedinUrl")}
                    placeholder="https://linkedin.com/in/username"
                  />
                </div>

                <div>
                  <Label htmlFor="mediumUrl">Medium URL</Label>
                  <Input
                    id="mediumUrl"
                    {...register("mediumUrl")}
                    placeholder="https://medium.com/@username"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end">
          <Button type="submit" disabled={saving}>
            <Save className="mr-2 h-4 w-4" />
            {saving ? "Saving..." : "Save Settings"}
          </Button>
        </div>
      </form>
    </div>
  )
}
