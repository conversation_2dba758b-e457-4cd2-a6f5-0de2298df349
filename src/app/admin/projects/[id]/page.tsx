"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import ProjectForm from "@/components/admin/ProjectForm"
import AdminLoading from "@/components/admin/AdminLoading"
import AdminError from "@/components/admin/AdminError"

export default function EditProjectPage() {
  const params = useParams()
  const projectId = params.id as string
  const [project, setProject] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchProject()
  }, [projectId])

  const fetchProject = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/admin/projects/${projectId}`)
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Project not found")
        }
        throw new Error("Failed to fetch project")
      }
      
      const data = await response.json()
      setProject(data)
    } catch (error: any) {
      setError(error.message)
      console.error("Error fetching project:", error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return <AdminLoading message="Loading project..." />
  }

  if (error) {
    return <AdminError message={error} onRetry={fetchProject} />
  }

  return <ProjectForm projectId={projectId} initialData={project} />
}
