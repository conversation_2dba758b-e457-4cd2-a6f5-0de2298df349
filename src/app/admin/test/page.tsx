"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, Loader2 } from "lucide-react"

interface TestResult {
  name: string
  status: "pending" | "success" | "error"
  message?: string
}

export default function AdminTestPage() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: "Admin Stats API", status: "pending" },
    { name: "Projects API", status: "pending" },
    { name: "Skills API", status: "pending" },
    { name: "Education API", status: "pending" },
    { name: "Analytics API", status: "pending" },
    { name: "Public Projects API", status: "pending" },
    { name: "Public Skills API", status: "pending" },
    { name: "Public Education API", status: "pending" },
  ])
  const [running, setRunning] = useState(false)

  const runTests = async () => {
    setRunning(true)
    const testEndpoints = [
      { name: "Admin Stats API", url: "/api/admin/stats" },
      { name: "Projects API", url: "/api/admin/projects" },
      { name: "Skills API", url: "/api/admin/skills" },
      { name: "Education API", url: "/api/admin/education" },
      { name: "Analytics API", url: "/api/admin/analytics" },
      { name: "Public Projects API", url: "/api/projects" },
      { name: "Public Skills API", url: "/api/skills" },
      { name: "Public Education API", url: "/api/education" },
    ]

    for (let i = 0; i < testEndpoints.length; i++) {
      const test = testEndpoints[i]
      
      try {
        const response = await fetch(test.url)
        const data = await response.json()
        
        setTests(prev => prev.map((t, idx) => 
          idx === i 
            ? { 
                ...t, 
                status: response.ok ? "success" : "error",
                message: response.ok ? `✓ ${Object.keys(data).length} fields returned` : `✗ ${response.status}: ${data.error || 'Unknown error'}`
              }
            : t
        ))
      } catch (error) {
        setTests(prev => prev.map((t, idx) => 
          idx === i 
            ? { 
                ...t, 
                status: "error",
                message: `✗ Network error: ${error instanceof Error ? error.message : 'Unknown error'}`
              }
            : t
        ))
      }
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    setRunning(false)
  }

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "error":
        return <XCircle className="h-5 w-5 text-red-600" />
      default:
        return <Loader2 className="h-5 w-5 text-gray-400 animate-spin" />
    }
  }

  const getStatusBadge = (status: TestResult["status"]) => {
    switch (status) {
      case "success":
        return <Badge variant="default" className="bg-green-600">Passed</Badge>
      case "error":
        return <Badge variant="destructive">Failed</Badge>
      default:
        return <Badge variant="secondary">Pending</Badge>
    }
  }

  const successCount = tests.filter(t => t.status === "success").length
  const errorCount = tests.filter(t => t.status === "error").length
  const pendingCount = tests.filter(t => t.status === "pending").length

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Admin Panel Tests</h1>
        <p className="text-gray-600 mt-1">
          Test all API endpoints and functionality
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{successCount}</div>
            <p className="text-sm text-gray-500">Tests Passed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{errorCount}</div>
            <p className="text-sm text-gray-500">Tests Failed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">{pendingCount}</div>
            <p className="text-sm text-gray-500">Tests Pending</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>API Endpoint Tests</CardTitle>
              <CardDescription>
                Testing all admin and public API endpoints
              </CardDescription>
            </div>
            <Button onClick={runTests} disabled={running}>
              {running ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running Tests...
                </>
              ) : (
                "Run Tests"
              )}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {tests.map((test, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(test.status)}
                  <div>
                    <p className="font-medium">{test.name}</p>
                    {test.message && (
                      <p className="text-sm text-gray-500">{test.message}</p>
                    )}
                  </div>
                </div>
                {getStatusBadge(test.status)}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Manual Testing Checklist</CardTitle>
          <CardDescription>
            Additional tests to perform manually
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="login" />
              <label htmlFor="login">✓ Login with admin credentials works</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="projects" />
              <label htmlFor="projects">✓ Create, edit, delete projects</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="skills" />
              <label htmlFor="skills">✓ Manage technical and soft skills</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="education" />
              <label htmlFor="education">✓ Add and edit education entries</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="media" />
              <label htmlFor="media">✓ Upload and manage media files</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="frontend" />
              <label htmlFor="frontend">✓ Frontend displays admin data correctly</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="responsive" />
              <label htmlFor="responsive">✓ Admin panel is responsive on mobile</label>
            </div>
            <div className="flex items-center space-x-2">
              <input type="checkbox" id="security" />
              <label htmlFor="security">✓ Protected routes require authentication</label>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
