import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import Script from "next/script";
import "./globals.css";
import { generateMetadata, generateStructuredData, generateWebsiteStructuredData } from "@/components/seo/SEOHead";
import ErrorBoundary from "@/components/error/ErrorBoundary";

const poppins = Poppins({
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  style: ['normal', 'italic'],
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap", // Improve font loading performance
  preload: true,
  fallback: ['system-ui', 'arial', 'sans-serif'], // Add fallback fonts
});

export const metadata: Metadata = generateMetadata({
  title: "Anas Altaf | Portfolio - Full Stack Developer",
  description: "Passionate full-stack developer specializing in web development, AI, automation, and modern technologies. Explore my projects, skills, and experience in React, Next.js, Python, and more.",
  keywords: "Anas <PERSON>af, Portfolio, Full Stack Developer, Web Developer, React, Next.js, TypeScript, Python, AI, Machine Learning, Automation, Software Engineer, Frontend, Backend",
  url: "https://anas-altaf.github.io",
  image: "/assets/images/og-image.jpg",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const personStructuredData = generateStructuredData({
    name: "Anas Altaf",
    jobTitle: "Full Stack Developer",
    description: "Passionate full-stack developer specializing in web development, AI, automation, and modern technologies.",
    url: "https://anas-altaf.github.io",
    image: "/assets/images/personal_img_2.webp",
    email: "<EMAIL>",
    sameAs: [
      "https://github.com/Anas-Altaf",
      "https://linkedin.com/in/anas-altaf",
      "https://medium.com/@anasaltaf",
    ],
  });
  const websiteStructuredData = generateWebsiteStructuredData();

  return (
    <html lang="en" className="scroll-smooth">
      <head>
        {/* Preconnect to external domains for better performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link rel="preconnect" href="https://cdn.jsdelivr.net" />
        <link rel="preconnect" href="https://cdnjs.cloudflare.com" />

        {/* DNS prefetch for external resources */}
        <link rel="dns-prefetch" href="https://www.google-analytics.com" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(personStructuredData),
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(websiteStructuredData),
          }}
        />
      </head>
      <body className={`${poppins.className} dark font-poppins antialiased`}>
        <ErrorBoundary>
          {children}
        </ErrorBoundary>

        {/* Load FontAwesome CSS */}
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css"
          integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />

        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-QCBYFJ1GR5"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-QCBYFJ1GR5', {
              page_title: document.title,
              page_location: window.location.href,
            });
          `}
        </Script>
      </body>
    </html>
  );
}
