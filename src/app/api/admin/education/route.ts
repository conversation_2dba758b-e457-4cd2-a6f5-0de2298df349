import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const education = await prisma.education.findMany({
      orderBy: [
        { current: "desc" },
        { order: "asc" },
        { createdAt: "desc" }
      ],
    })

    return NextResponse.json(education)
  } catch (error) {
    console.error("Failed to fetch education:", error)
    return NextResponse.json(
      { error: "Failed to fetch education" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      degree,
      institution,
      dateRange,
      location,
      country,
      eqfLevel,
      logo,
      link,
      current = false,
      order = 0
    } = body

    // Validate required fields
    if (!degree || !institution || !dateRange || !location || !country) {
      return NextResponse.json(
        { error: "Degree, institution, date range, location, and country are required" },
        { status: 400 }
      )
    }

    const education = await prisma.education.create({
      data: {
        degree,
        institution,
        dateRange,
        location,
        country,
        eqfLevel,
        logo,
        link,
        current,
        order,
      },
    })

    return NextResponse.json(education, { status: 201 })
  } catch (error) {
    console.error("Failed to create education:", error)
    return NextResponse.json(
      { error: "Failed to create education" },
      { status: 500 }
    )
  }
}
