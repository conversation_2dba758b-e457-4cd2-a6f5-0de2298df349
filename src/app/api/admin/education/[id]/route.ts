import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const education = await prisma.education.findUnique({
      where: { id: params.id },
    })

    if (!education) {
      return NextResponse.json({ error: "Education not found" }, { status: 404 })
    }

    return NextResponse.json(education)
  } catch (error) {
    console.error("Failed to fetch education:", error)
    return NextResponse.json(
      { error: "Failed to fetch education" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const updateData: any = {}

    // Handle allowed fields
    const allowedFields = [
      'degree', 'institution', 'dateRange', 'location', 'country', 
      'eqfLevel', 'logo', 'link', 'current', 'order'
    ]
    
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field]
      }
    })

    const education = await prisma.education.update({
      where: { id: params.id },
      data: updateData,
    })

    return NextResponse.json(education)
  } catch (error) {
    console.error("Failed to update education:", error)
    return NextResponse.json(
      { error: "Failed to update education" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await prisma.education.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to delete education:", error)
    return NextResponse.json(
      { error: "Failed to delete education" },
      { status: 500 }
    )
  }
}
