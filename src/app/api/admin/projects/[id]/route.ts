import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const project = await prisma.project.findUnique({
      where: { id: params.id },
    })

    if (!project) {
      return NextResponse.json({ error: "Project not found" }, { status: 404 })
    }

    // Parse JSON fields
    const formattedProject = {
      ...project,
      content: JSON.parse(project.content),
      images: JSON.parse(project.images),
      videos: JSON.parse(project.videos),
      technologies: JSON.parse(project.technologies),
    }

    return NextResponse.json(formattedProject)
  } catch (error) {
    console.error("Failed to fetch project:", error)
    return NextResponse.json(
      { error: "Failed to fetch project" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const updateData: any = {}

    // Handle JSON fields
    if (body.content !== undefined) {
      updateData.content = JSON.stringify(body.content)
    }
    if (body.images !== undefined) {
      updateData.images = JSON.stringify(body.images)
    }
    if (body.videos !== undefined) {
      updateData.videos = JSON.stringify(body.videos)
    }
    if (body.technologies !== undefined) {
      updateData.technologies = JSON.stringify(body.technologies)
    }

    // Handle other fields
    const allowedFields = [
      'name', 'description', 'category', 'link', 'featured', 'published', 'order'
    ]
    
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field]
      }
    })

    const project = await prisma.project.update({
      where: { id: params.id },
      data: updateData,
    })

    return NextResponse.json(project)
  } catch (error) {
    console.error("Failed to update project:", error)
    return NextResponse.json(
      { error: "Failed to update project" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await prisma.project.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to delete project:", error)
    return NextResponse.json(
      { error: "Failed to delete project" },
      { status: 500 }
    )
  }
}
