import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const projects = await prisma.project.findMany({
      orderBy: [
        { featured: "desc" },
        { order: "asc" },
        { createdAt: "desc" }
      ],
      select: {
        id: true,
        name: true,
        description: true,
        category: true,
        featured: true,
        published: true,
        technologies: true,
        createdAt: true,
        updatedAt: true,
      }
    })

    // Parse JSON fields
    const formattedProjects = projects.map(project => ({
      ...project,
      technologies: JSON.parse(project.technologies),
    }))

    return NextResponse.json(formattedProjects)
  } catch (error) {
    console.error("Failed to fetch projects:", error)
    return NextResponse.json(
      { error: "Failed to fetch projects" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      content,
      images,
      videos,
      technologies,
      category,
      link,
      featured = false,
      published = true,
      order = 0
    } = body

    // Validate required fields
    if (!name || !description || !category) {
      return NextResponse.json(
        { error: "Name, description, and category are required" },
        { status: 400 }
      )
    }

    const project = await prisma.project.create({
      data: {
        name,
        description,
        content: JSON.stringify(content || []),
        images: JSON.stringify(images || []),
        videos: JSON.stringify(videos || []),
        technologies: JSON.stringify(technologies || []),
        category,
        link,
        featured,
        published,
        order,
      },
    })

    return NextResponse.json(project, { status: 201 })
  } catch (error) {
    console.error("Failed to create project:", error)
    return NextResponse.json(
      { error: "Failed to create project" },
      { status: 500 }
    )
  }
}
