import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user?.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get settings from database or return defaults
    let settings = await prisma.siteSettings.findFirst();
    
    if (!settings) {
      // Create default settings if none exist
      settings = await prisma.siteSettings.create({
        data: {
          siteName: "Anas Altaf Portfolio",
          siteDescription: "Software Engineer & AI Enthusiast",
          heroTitle: "Hi, I'm Anas Altaf",
          heroSubtitle: "Software Engineer & AI Enthusiast",
          contactEmail: "<EMAIL>",
          heroImage: "/assets/images/personal-img-art.webp",
          logoImage: "/assets/images/personal-img-art.webp",
          footerImage: "/assets/images/personal-img-art.webp",
          favicon: "/favicon.ico",
          githubUrl: "https://github.com/Anas-Altaf",
          linkedinUrl: "https://linkedin.com/in/anas-altaf",
          mediumUrl: "https://medium.com/@anas-altaf",
        },
      });
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || session.user?.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();

    // Validate required fields
    const requiredFields = ["siteName", "siteDescription", "heroTitle", "heroSubtitle", "contactEmail"];
    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Update or create settings
    const settings = await prisma.siteSettings.upsert({
      where: { id: 1 }, // Assuming single settings record
      update: {
        siteName: data.siteName,
        siteDescription: data.siteDescription,
        heroTitle: data.heroTitle,
        heroSubtitle: data.heroSubtitle,
        contactEmail: data.contactEmail,
        heroImage: data.heroImage || null,
        logoImage: data.logoImage || null,
        footerImage: data.footerImage || null,
        favicon: data.favicon || null,
        githubUrl: data.githubUrl || null,
        linkedinUrl: data.linkedinUrl || null,
        mediumUrl: data.mediumUrl || null,
        updatedAt: new Date(),
      },
      create: {
        id: 1,
        siteName: data.siteName,
        siteDescription: data.siteDescription,
        heroTitle: data.heroTitle,
        heroSubtitle: data.heroSubtitle,
        contactEmail: data.contactEmail,
        heroImage: data.heroImage || null,
        logoImage: data.logoImage || null,
        footerImage: data.footerImage || null,
        favicon: data.favicon || null,
        githubUrl: data.githubUrl || null,
        linkedinUrl: data.linkedinUrl || null,
        mediumUrl: data.mediumUrl || null,
      },
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error saving settings:", error);
    return NextResponse.json(
      { error: "Failed to save settings" },
      { status: 500 }
    );
  }
}
