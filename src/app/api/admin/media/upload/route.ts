import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { writeFile, mkdir } from "fs/promises"
import { join } from "path"
import { existsSync } from "fs"
import sharp from "sharp"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      return NextResponse.json({ error: "File too large" }, { status: 400 })
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), "public", "uploads")
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true })
    }

    // Generate unique filename
    const timestamp = Date.now()
    const originalName = file.name
    const extension = originalName.split(".").pop()
    const filename = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`
    const filepath = join(uploadsDir, filename)

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Optimize images using Sharp
    let finalBuffer = buffer
    let mimeType = file.type

    if (file.type.startsWith("image/")) {
      try {
        // Optimize image: resize if too large, convert to WebP for better compression
        const image = sharp(buffer)
        const metadata = await image.metadata()
        
        let processedImage = image
        
        // Resize if width > 1920px
        if (metadata.width && metadata.width > 1920) {
          processedImage = processedImage.resize(1920, null, {
            withoutEnlargement: true
          })
        }

        // Convert to WebP for better compression (except for SVG)
        if (file.type !== "image/svg+xml") {
          processedImage = processedImage.webp({ quality: 85 })
          mimeType = "image/webp"
          // Update filename extension
          const newFilename = filename.replace(/\.[^/.]+$/, ".webp")
          const newFilepath = join(uploadsDir, newFilename)
          finalBuffer = await processedImage.toBuffer()
          await writeFile(newFilepath, finalBuffer)
          
          // Update filename for database
          const finalFilename = newFilename
          const url = `/uploads/${finalFilename}`
          
          // Determine category
          const category = getFileCategory(mimeType)

          // Save to database
          const media = await prisma.media.create({
            data: {
              filename: finalFilename,
              originalName,
              mimeType,
              size: finalBuffer.length,
              url,
              category,
            },
          })

          return NextResponse.json(media, { status: 201 })
        }
      } catch (error) {
        console.error("Image optimization failed:", error)
        // Fall back to original file if optimization fails
      }
    }

    // Write original file (for non-images or if optimization failed)
    await writeFile(filepath, finalBuffer)
    
    const url = `/uploads/${filename}`
    const category = getFileCategory(mimeType)

    // Save to database
    const media = await prisma.media.create({
      data: {
        filename,
        originalName,
        mimeType,
        size: file.size,
        url,
        category,
      },
    })

    return NextResponse.json(media, { status: 201 })
  } catch (error) {
    console.error("Failed to upload file:", error)
    return NextResponse.json(
      { error: "Failed to upload file" },
      { status: 500 }
    )
  }
}

function getFileCategory(mimeType: string): string {
  if (mimeType.startsWith("image/")) return "image"
  if (mimeType.startsWith("video/")) return "video"
  if (mimeType === "application/pdf") return "document"
  return "other"
}

// Increase the body size limit for file uploads
export const runtime = "nodejs"
