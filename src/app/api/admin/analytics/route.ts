import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Get basic counts
    const [
      totalProjects,
      publishedProjects,
      featuredProjects,
      totalSkills,
      totalSoftSkills,
      totalEducation,
      totalMedia,
      recentProjects,
      recentMedia
    ] = await Promise.all([
      prisma.project.count(),
      prisma.project.count({ where: { published: true } }),
      prisma.project.count({ where: { featured: true } }),
      prisma.skill.count(),
      prisma.softSkill.count(),
      prisma.education.count(),
      prisma.media.count(),
      prisma.project.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          name: true,
          category: true,
          featured: true,
          published: true,
          createdAt: true,
        }
      }),
      prisma.media.findMany({
        take: 5,
        orderBy: { createdAt: "desc" },
        select: {
          id: true,
          originalName: true,
          category: true,
          size: true,
          createdAt: true,
        }
      })
    ])

    // Calculate project categories distribution
    const projectsByCategory = await prisma.project.groupBy({
      by: ['category'],
      _count: {
        category: true,
      },
      where: { published: true }
    })

    // Calculate skills categories distribution
    const skillsByCategory = await prisma.skill.groupBy({
      by: ['category'],
      _count: {
        category: true,
      }
    })

    // Calculate media size statistics
    const mediaStats = await prisma.media.aggregate({
      _sum: {
        size: true,
      },
      _avg: {
        size: true,
      },
      _count: {
        id: true,
      }
    })

    // Calculate growth over time (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const [recentProjectsCount, recentMediaCount] = await Promise.all([
      prisma.project.count({
        where: {
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      }),
      prisma.media.count({
        where: {
          createdAt: {
            gte: thirtyDaysAgo
          }
        }
      })
    ])

    const analytics = {
      overview: {
        totalProjects,
        publishedProjects,
        featuredProjects,
        totalSkills,
        totalSoftSkills,
        totalEducation,
        totalMedia,
        publishedRate: totalProjects > 0 ? Math.round((publishedProjects / totalProjects) * 100) : 0,
        featuredRate: totalProjects > 0 ? Math.round((featuredProjects / totalProjects) * 100) : 0,
      },
      distributions: {
        projectsByCategory: projectsByCategory.map(item => ({
          category: item.category,
          count: item._count.category
        })),
        skillsByCategory: skillsByCategory.map(item => ({
          category: item.category,
          count: item._count.category
        })),
      },
      media: {
        totalSize: mediaStats._sum.size || 0,
        averageSize: Math.round(mediaStats._avg.size || 0),
        totalFiles: mediaStats._count.id,
      },
      growth: {
        recentProjects: recentProjectsCount,
        recentMedia: recentMediaCount,
      },
      recent: {
        projects: recentProjects,
        media: recentMedia,
      }
    }

    return NextResponse.json(analytics)
  } catch (error) {
    console.error("Failed to fetch analytics:", error)
    return NextResponse.json(
      { error: "Failed to fetch analytics" },
      { status: 500 }
    )
  }
}
