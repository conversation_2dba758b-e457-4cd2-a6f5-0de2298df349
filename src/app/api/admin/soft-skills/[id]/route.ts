import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const softSkill = await prisma.softSkill.findUnique({
      where: { id: params.id },
    })

    if (!softSkill) {
      return NextResponse.json({ error: "Soft skill not found" }, { status: 404 })
    }

    return NextResponse.json(softSkill)
  } catch (error) {
    console.error("Failed to fetch soft skill:", error)
    return NextResponse.json(
      { error: "Failed to fetch soft skill" },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const updateData: any = {}

    // Handle allowed fields
    const allowedFields = ['name', 'order']
    
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field]
      }
    })

    // Check for duplicate name if updating name
    if (updateData.name) {
      const existingSoftSkill = await prisma.softSkill.findFirst({
        where: { 
          name: updateData.name,
          id: { not: params.id }
        },
      })

      if (existingSoftSkill) {
        return NextResponse.json(
          { error: "Soft skill with this name already exists" },
          { status: 400 }
        )
      }
    }

    const softSkill = await prisma.softSkill.update({
      where: { id: params.id },
      data: updateData,
    })

    return NextResponse.json(softSkill)
  } catch (error) {
    console.error("Failed to update soft skill:", error)
    return NextResponse.json(
      { error: "Failed to update soft skill" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await prisma.softSkill.delete({
      where: { id: params.id },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Failed to delete soft skill:", error)
    return NextResponse.json(
      { error: "Failed to delete soft skill" },
      { status: 500 }
    )
  }
}
