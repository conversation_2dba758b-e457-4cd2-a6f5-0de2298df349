import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const softSkills = await prisma.softSkill.findMany({
      orderBy: [
        { order: "asc" },
        { createdAt: "desc" }
      ],
    })

    return NextResponse.json(softSkills)
  } catch (error) {
    console.error("Failed to fetch soft skills:", error)
    return NextResponse.json(
      { error: "Failed to fetch soft skills" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { name, order = 0 } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: "Name is required" },
        { status: 400 }
      )
    }

    // Check if soft skill already exists
    const existingSoftSkill = await prisma.softSkill.findUnique({
      where: { name },
    })

    if (existingSoftSkill) {
      return NextResponse.json(
        { error: "Soft skill already exists" },
        { status: 400 }
      )
    }

    const softSkill = await prisma.softSkill.create({
      data: {
        name,
        order,
      },
    })

    return NextResponse.json(softSkill, { status: 201 })
  } catch (error) {
    console.error("Failed to create soft skill:", error)
    return NextResponse.json(
      { error: "Failed to create soft skill" },
      { status: 500 }
    )
  }
}
