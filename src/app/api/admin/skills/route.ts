import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const skills = await prisma.skill.findMany({
      orderBy: [
        { order: "asc" },
        { createdAt: "desc" }
      ],
    })

    // Parse JSON fields
    const formattedSkills = skills.map(skill => ({
      ...skill,
      technologies: JSON.parse(skill.technologies),
    }))

    return NextResponse.json(formattedSkills)
  } catch (error) {
    console.error("Failed to fetch skills:", error)
    return NextResponse.json(
      { error: "Failed to fetch skills" },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      title,
      description,
      technologies,
      category,
      order = 0
    } = body

    // Validate required fields
    if (!title || !description || !category) {
      return NextResponse.json(
        { error: "Title, description, and category are required" },
        { status: 400 }
      )
    }

    const skill = await prisma.skill.create({
      data: {
        title,
        description,
        technologies: JSON.stringify(technologies || []),
        category,
        order,
      },
    })

    return NextResponse.json(skill, { status: 201 })
  } catch (error) {
    console.error("Failed to create skill:", error)
    return NextResponse.json(
      { error: "Failed to create skill" },
      { status: 500 }
    )
  }
}
