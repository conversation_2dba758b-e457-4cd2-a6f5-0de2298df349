import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const projects = await prisma.project.findMany({
      where: { published: true },
      orderBy: [
        { featured: "desc" },
        { order: "asc" },
        { createdAt: "desc" }
      ],
    })

    // Transform admin data to match frontend interface
    const formattedProjects = projects.map(project => {
      const content = JSON.parse(project.content)
      const images = JSON.parse(project.images)
      const videos = JSON.parse(project.videos)
      const technologies = JSON.parse(project.technologies)

      return {
        pName: project.name,
        desc: project.description,
        img: images[0] || "/assets/images/projects/default.webp", // First image as main image
        video: videos[0] || "", // First video
        images: images,
        icons: technologies.map((tech: string) => tech.toLowerCase().replace(/[^a-z0-9]/g, '')), // Convert to icon names
        category: project.category,
        link: project.link || "",
        content: Array.isArray(content) ? content : [project.description],
        isCurrent: project.featured, // Use featured as current indicator
      }
    })

    return NextResponse.json(formattedProjects)
  } catch (error) {
    console.error("Failed to fetch projects:", error)
    return NextResponse.json(
      { error: "Failed to fetch projects" },
      { status: 500 }
    )
  }
}

export const revalidate = 60 // Revalidate every 60 seconds
