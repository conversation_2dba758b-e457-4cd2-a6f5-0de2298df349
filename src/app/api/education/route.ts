import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const education = await prisma.education.findMany({
      orderBy: [
        { current: "desc" },
        { order: "asc" },
        { createdAt: "desc" }
      ],
    })

    // Transform admin data to match frontend interface
    const formattedEducation = education.map(edu => ({
      degree: edu.degree,
      institution: edu.institution,
      dateRange: edu.dateRange,
      location: edu.location,
      country: edu.country,
      eqfLevel: edu.eqfLevel || "",
      link: edu.link || "",
      logo: edu.logo || "/assets/images/logos/default.png",
    }))

    return NextResponse.json(formattedEducation)
  } catch (error) {
    console.error("Failed to fetch education:", error)
    return NextResponse.json(
      { error: "Failed to fetch education" },
      { status: 500 }
    )
  }
}

export const revalidate = 60 // Revalidate every 60 seconds
