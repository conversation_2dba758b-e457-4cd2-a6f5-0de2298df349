import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const [skills, softSkills] = await Promise.all([
      prisma.skill.findMany({
        orderBy: { order: "asc" },
      }),
      prisma.softSkill.findMany({
        orderBy: { order: "asc" },
      })
    ])

    // Transform admin data to match frontend interface
    const formattedSkills = skills.map(skill => {
      const technologies = JSON.parse(skill.technologies)
      
      return {
        title: skill.title,
        description: skill.description,
        stack: technologies,
      }
    })

    const formattedSoftSkills = softSkills.map(skill => skill.name)

    return NextResponse.json({
      skills: formattedSkills,
      softSkills: formattedSoftSkills,
    })
  } catch (error) {
    console.error("Failed to fetch skills:", error)
    return NextResponse.json(
      { error: "Failed to fetch skills" },
      { status: 500 }
    )
  }
}

export const revalidate = 60 // Revalidate every 60 seconds
