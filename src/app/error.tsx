"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { Alert<PERSON>riangle, Refresh<PERSON><PERSON>, Home } from "lucide-react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Global error:", error);
    
    // You can integrate with error reporting services here
    // Example: Sentry.captureException(error);
  }, [error]);

  const handleGoHome = () => {
    window.location.href = "/";
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-bg-p px-4">
      <motion.div
        className="max-w-md w-full text-center"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          className="mb-8"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
        >
          <AlertTriangle className="mx-auto h-16 w-16 text-red-500" />
        </motion.div>

        <motion.h1
          className="text-2xl font-bold text-white mb-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          Something went wrong!
        </motion.h1>

        <motion.p
          className="text-txt-desc mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          We encountered an unexpected error. Please try again or return to the homepage.
        </motion.p>

        {process.env.NODE_ENV === "development" && (
          <motion.details
            className="mb-8 text-left bg-fg-p p-4 rounded-lg"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5 }}
          >
            <summary className="cursor-pointer text-red-400 font-medium mb-2">
              Error Details (Development)
            </summary>
            <pre className="text-xs text-gray-300 overflow-auto whitespace-pre-wrap">
              {error.message}
              {error.stack && (
                <>
                  {"\n\nStack Trace:\n"}
                  {error.stack}
                </>
              )}
            </pre>
          </motion.details>
        )}

        <motion.div
          className="flex flex-col sm:flex-row gap-4 justify-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <button
            onClick={reset}
            className="flex items-center justify-center gap-2 px-6 py-3 bg-acc text-white rounded-lg hover:bg-acc/80 transition-colors duration-200"
          >
            <RefreshCw className="h-4 w-4" />
            Try Again
          </button>

          <button
            onClick={handleGoHome}
            className="flex items-center justify-center gap-2 px-6 py-3 bg-fg-s text-white rounded-lg hover:bg-fg-s/80 transition-colors duration-200"
          >
            <Home className="h-4 w-4" />
            Go Home
          </button>
        </motion.div>
      </motion.div>
    </div>
  );
}
