import { MetadataRoute } from 'next';

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: '<PERSON><PERSON>',
    short_name: '<PERSON><PERSON>',
    description: 'Passionate full-stack developer specializing in web development, AI, automation, and modern technologies.',
    start_url: '/',
    display: 'standalone',
    background_color: '#181818',
    theme_color: '#10b981',
    orientation: 'portrait-primary',
    categories: ['portfolio', 'developer', 'technology'],
    lang: 'en',
    icons: [
      {
        src: '/favicon-16x16.png',
        sizes: '16x16',
        type: 'image/png',
      },
      {
        src: '/favicon-32x32.png',
        sizes: '32x32',
        type: 'image/png',
      },
      {
        src: '/apple-touch-icon.png',
        sizes: '180x180',
        type: 'image/png',
      },
      {
        src: '/android-chrome-192x192.png',
        sizes: '192x192',
        type: 'image/png',
      },
      {
        src: '/android-chrome-512x512.png',
        sizes: '512x512',
        type: 'image/png',
      },
    ],
  };
}
