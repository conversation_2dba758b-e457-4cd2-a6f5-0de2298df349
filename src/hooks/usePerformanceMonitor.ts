"use client";

import { useEffect, useCallback } from "react";

interface PerformanceMetrics {
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
}

interface UsePerformanceMonitorOptions {
  enableLogging?: boolean;
  enableAnalytics?: boolean;
  thresholds?: {
    lcp?: number;
    fid?: number;
    cls?: number;
  };
}

export const usePerformanceMonitor = (options: UsePerformanceMonitorOptions = {}) => {
  const {
    enableLogging = process.env.NODE_ENV === "development",
    enableAnalytics = true,
    thresholds = {
      lcp: 2500, // Good: <= 2.5s
      fid: 100,  // Good: <= 100ms
      cls: 0.1,  // Good: <= 0.1
    },
  } = options;

  const logMetric = useCallback((name: string, value: number, rating: string) => {
    if (enableLogging) {
      console.log(`Performance Metric - ${name}:`, {
        value: Math.round(value),
        rating,
        threshold: thresholds[name as keyof typeof thresholds],
      });
    }
  }, [enableLogging, thresholds]);

  const sendToAnalytics = useCallback((metric: { name: string; value: number; rating: string }) => {
    if (enableAnalytics && typeof window !== "undefined") {
      // Send to Google Analytics 4
      if (window.gtag) {
        window.gtag("event", "web_vitals", {
          event_category: "Performance",
          event_label: metric.name,
          value: Math.round(metric.value),
          custom_parameter_1: metric.rating,
        });
      }

      // You can also send to other analytics services here
      // Example: Sentry, DataDog, etc.
    }
  }, [enableAnalytics]);

  const getRating = useCallback((name: string, value: number): string => {
    const threshold = thresholds[name as keyof typeof thresholds];
    if (!threshold) return "unknown";

    switch (name) {
      case "lcp":
        return value <= 2500 ? "good" : value <= 4000 ? "needs-improvement" : "poor";
      case "fid":
        return value <= 100 ? "good" : value <= 300 ? "needs-improvement" : "poor";
      case "cls":
        return value <= 0.1 ? "good" : value <= 0.25 ? "needs-improvement" : "poor";
      default:
        return "unknown";
    }
  }, [thresholds]);

  const measureWebVitals = useCallback(() => {
    if (typeof window === "undefined" || !window.performance) return;

    // Measure LCP (Largest Contentful Paint)
    if ("PerformanceObserver" in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          if (lastEntry) {
            const value = lastEntry.startTime;
            const rating = getRating("lcp", value);
            logMetric("lcp", value, rating);
            sendToAnalytics({ name: "LCP", value, rating });
          }
        });
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });

        // Measure FID (First Input Delay)
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            const value = entry.processingStart - entry.startTime;
            const rating = getRating("fid", value);
            logMetric("fid", value, rating);
            sendToAnalytics({ name: "FID", value, rating });
          });
        });
        fidObserver.observe({ entryTypes: ["first-input"] });

        // Measure CLS (Cumulative Layout Shift)
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          const rating = getRating("cls", clsValue);
          logMetric("cls", clsValue, rating);
          sendToAnalytics({ name: "CLS", value: clsValue, rating });
        });
        clsObserver.observe({ entryTypes: ["layout-shift"] });

        // Cleanup observers on page unload
        const cleanup = () => {
          lcpObserver.disconnect();
          fidObserver.disconnect();
          clsObserver.disconnect();
        };

        window.addEventListener("beforeunload", cleanup);
        return cleanup;
      } catch (error) {
        console.warn("Performance monitoring setup failed:", error);
      }
    }
  }, [getRating, logMetric, sendToAnalytics]);

  const measureCustomMetrics = useCallback(() => {
    if (typeof window === "undefined" || !window.performance) return;

    // Measure navigation timing
    const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
    if (navigation) {
      const ttfb = navigation.responseStart - navigation.requestStart;
      const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.navigationStart;
      const loadComplete = navigation.loadEventEnd - navigation.navigationStart;

      if (enableLogging) {
        console.log("Navigation Metrics:", {
          ttfb: Math.round(ttfb),
          domContentLoaded: Math.round(domContentLoaded),
          loadComplete: Math.round(loadComplete),
        });
      }

      if (enableAnalytics && window.gtag) {
        window.gtag("event", "navigation_timing", {
          event_category: "Performance",
          ttfb: Math.round(ttfb),
          dom_content_loaded: Math.round(domContentLoaded),
          load_complete: Math.round(loadComplete),
        });
      }
    }

    // Measure resource timing
    const resources = performance.getEntriesByType("resource");
    const slowResources = resources.filter((resource: any) => resource.duration > 1000);
    
    if (slowResources.length > 0 && enableLogging) {
      console.warn("Slow resources detected:", slowResources.map((r: any) => ({
        name: r.name,
        duration: Math.round(r.duration),
        size: r.transferSize,
      })));
    }
  }, [enableLogging, enableAnalytics]);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Wait for page load to measure metrics
    const measureMetrics = () => {
      measureWebVitals();
      measureCustomMetrics();
    };

    if (document.readyState === "complete") {
      measureMetrics();
    } else {
      window.addEventListener("load", measureMetrics);
      return () => window.removeEventListener("load", measureMetrics);
    }
  }, [measureWebVitals, measureCustomMetrics]);

  return {
    measureWebVitals,
    measureCustomMetrics,
  };
};

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag?: (...args: any[]) => void;
  }
}
