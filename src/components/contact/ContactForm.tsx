"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Send, CheckCircle, AlertCircle } from "lucide-react";

interface FormData {
  name: string;
  email: string;
  subject: string;
  message: string;
}

const ContactForm: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");

  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    subject: "",
    message: "",
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitStatus("idle");

    try {
      // Simulate form submission - replace with actual API call
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // For now, just log the form data
      console.log("Form submitted:", values);

      setSubmitStatus("success");
      form.reset();
    } catch (error) {
      console.error("Form submission error:", error);
      setSubmitStatus("error");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto px-6 py-8">
      <Card className="bg-fg-p/80 backdrop-blur-md border-fg-s/50 shadow-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-white">
            Get In Touch
          </CardTitle>
          <CardDescription className="text-txt-desc">
            Have a project in mind? Let's discuss how we can work together.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-white">Name</Label>
                <Input
                  placeholder="Your name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="bg-bg-p/50 border-fg-s text-white placeholder:text-txt-desc focus:border-acc"
                />
              </div>
              <div>
                <Label className="text-white">Email</Label>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="bg-bg-p/50 border-fg-s text-white placeholder:text-txt-desc focus:border-acc"
                />
              </div>
            </div>

            <div>
              <Label className="text-white">Subject</Label>
              <Input
                placeholder="What's this about?"
                value={formData.subject}
                onChange={(e) => handleInputChange("subject", e.target.value)}
                className="bg-bg-p/50 border-fg-s text-white placeholder:text-txt-desc focus:border-acc"
              />
            </div>

            <div>
              <Label className="text-white">Message</Label>
              <Textarea
                placeholder="Tell me about your project..."
                value={formData.message}
                onChange={(e) => handleInputChange("message", e.target.value)}
                className="bg-bg-p/50 border-fg-s text-white placeholder:text-txt-desc focus:border-acc min-h-[120px]"
              />
            </div>

            {submitStatus === "success" && (
              <div className="flex items-center gap-2 text-green-500 bg-green-500/10 p-3 rounded-lg">
                <CheckCircle size={20} />
                <span>
                  Message sent successfully! I'll get back to you soon.
                </span>
              </div>
            )}

            {submitStatus === "error" && (
              <div className="flex items-center gap-2 text-red-500 bg-red-500/10 p-3 rounded-lg">
                <AlertCircle size={20} />
                <span>Failed to send message. Please try again.</span>
              </div>
            )}

            <Button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-acc to-green-600 hover:from-green-600 hover:to-acc text-black font-medium transition-all duration-300"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2" />
                  Sending...
                </>
              ) : (
                <>
                  <Send size={16} className="mr-2" />
                  Send Message
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default ContactForm;
