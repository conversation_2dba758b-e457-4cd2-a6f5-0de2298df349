"use client";

import { useEffect, useRef, Suspense } from "react";
import dynamic from "next/dynamic";
import ReactGA from "react-ga4";
import { usePerformanceMonitor } from "@/hooks/usePerformanceMonitor";
import ErrorBoundary from "./error/ErrorBoundary";
import LoadingSpinner from "./loading/LoadingSpinner";
import {
  ProjectCardSkeleton,
  BlogCardSkeleton,
  RepoCardSkeleton,
  SkillsSkeleton,
  EducationSkeleton,
  SectionSkeleton,
} from "./loading/SkeletonLoaders";

// Static imports for above-the-fold content
import Hero from "./header/Hero";
import MainHeading from "./header/MainHeading";

// Dynamic imports for below-the-fold content with loading states
const ReposSlider = dynamic(() => import("./repos/ReposSlider"), {
  loading: () => (
    <div className="flex justify-center space-x-4 px-6">
      {[...Array(3)].map((_, i) => (
        <RepoCardSkeleton key={i} />
      ))}
    </div>
  ),
  ssr: false, // Disable SSR for this component as it fetches external data
});

const Projects = dynamic(() => import("./projects/Projects"), {
  loading: () => (
    <div className="flex flex-col items-center space-y-10 py-10 px-6 mx-auto max-w-4xl">
      {[...Array(3)].map((_, i) => (
        <ProjectCardSkeleton key={i} />
      ))}
    </div>
  ),
});

const Skills = dynamic(() => import("./skills/Skills"), {
  loading: () => <SkillsSkeleton />,
});

const Education = dynamic(() => import("./education/Education"), {
  loading: () => <EducationSkeleton />,
});

const AIWorkflowsCarousel = dynamic(
  () => import("./ai-workflows/AIWorkflowsCarousel"),
  {
    loading: () => (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-acc"></div>
      </div>
    ),
  }
);

const Blogs = dynamic(() => import("./blogs/Blogs"), {
  loading: () => (
    <div className="max-w-6xl mx-auto mt-5 py-4 px-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <BlogCardSkeleton key={i} />
        ))}
      </div>
    </div>
  ),
  ssr: false, // Disable SSR for this component as it fetches external data
});

const ContactForm = dynamic(() => import("./contact/ContactForm"), {
  loading: () => <SectionSkeleton className="min-h-[400px]" />,
});

const Footer = dynamic(() => import("./Footer"), {
  loading: () => <SectionSkeleton className="h-32" />,
});

const ScrollToTop = dynamic(() => import("./effects/ScrollToTop"), {
  ssr: false,
});

const TRACKING_ID = "G-QCBYFJ1GR5";

export default function PortfolioClient() {
  // Initialize performance monitoring
  usePerformanceMonitor({
    enableLogging: process.env.NODE_ENV === "development",
    enableAnalytics: true,
  });

  // Initialize Google Analytics
  useEffect(() => {
    ReactGA.initialize(TRACKING_ID);
    ReactGA.send({ hitType: "pageview", page: window.location.pathname });

    // Track user engagement
    const trackEngagement = () => {
      ReactGA.event({
        category: "User Engagement",
        action: "Page Interaction",
        label: "Portfolio Visit",
      });
    };

    // Track scroll depth
    let maxScroll = 0;
    const trackScrollDepth = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) *
          100
      );
      if (scrollPercent > maxScroll && scrollPercent % 25 === 0) {
        maxScroll = scrollPercent;
        ReactGA.event({
          category: "User Engagement",
          action: "Scroll Depth",
          label: `${scrollPercent}%`,
          value: scrollPercent,
        });
      }
    };

    window.addEventListener("scroll", trackScrollDepth, { passive: true });
    window.addEventListener("click", trackEngagement, { passive: true });

    return () => {
      window.removeEventListener("scroll", trackScrollDepth);
      window.removeEventListener("click", trackEngagement);
    };
  }, []);

  // Refs for smooth scrolling
  const homeRef = useRef<HTMLDivElement>(null);
  const projectsRef = useRef<HTMLDivElement>(null);
  const educationRef = useRef<HTMLDivElement>(null);
  const skillsRef = useRef<HTMLDivElement>(null);
  const aiWorkflowsRef = useRef<HTMLDivElement>(null);
  const blogsRef = useRef<HTMLDivElement>(null);
  const contactRef = useRef<HTMLDivElement>(null);
  const aboutRef = useRef<HTMLDivElement>(null);

  // Navigation handler
  const handleNavClick = (section: string) => {
    switch (section) {
      case "Home":
        homeRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "Projects":
        projectsRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "Education":
        educationRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "Skills":
        skillsRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "AI Workflows":
        aiWorkflowsRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "Blogs":
        blogsRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "Contact":
        contactRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      case "About":
        aboutRef.current?.scrollIntoView({ behavior: "smooth" });
        break;
      default:
        break;
    }
  };

  return (
    <>
      {/* Above-the-fold content - loaded immediately */}
      <Hero ref={homeRef} onNavClick={handleNavClick} />

      {/* Below-the-fold content - loaded with Suspense and Error Boundaries */}
      <ErrorBoundary
        fallback={
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <p className="text-txt-desc mb-4">
                Failed to load repositories section
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-acc text-white rounded-lg hover:bg-acc/80 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        }
      >
        <MainHeading
          id="01"
          title="Repositories"
          description="Here is my code work, that I have touched so far"
        />
        <Suspense
          fallback={
            <div className="flex justify-center space-x-4 px-6">
              {[...Array(3)].map((_, i) => (
                <RepoCardSkeleton key={i} />
              ))}
            </div>
          }
        >
          <ReposSlider />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary
        fallback={
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <p className="text-txt-desc mb-4">
                Failed to load projects section
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-acc text-white rounded-lg hover:bg-acc/80 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        }
      >
        <MainHeading
          id="02"
          title="Selected Work"
          description="Some selected projects that I have worked on"
          ref={projectsRef}
        />
        <Suspense
          fallback={
            <div className="flex flex-col items-center space-y-10 py-10 px-6 mx-auto max-w-4xl">
              {[...Array(3)].map((_, i) => (
                <ProjectCardSkeleton key={i} />
              ))}
            </div>
          }
        >
          <Projects />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary>
        <MainHeading
          id="03"
          title="Key Skills & Expertise"
          description="Learning Journey's key skills that helped me to develop myself and my expertise"
          ref={skillsRef}
        />
        <Suspense fallback={<SkillsSkeleton />}>
          <Skills />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary
        fallback={
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <p className="text-txt-desc mb-4">
                Failed to load AI workflows section
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-acc text-white rounded-lg hover:bg-acc/80 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        }
      >
        <MainHeading
          id="04"
          title="AI & Automation Workflows"
          description="Intelligent automation solutions and AI-powered workflows I've developed"
          ref={aiWorkflowsRef}
        />
        <Suspense
          fallback={
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-acc"></div>
            </div>
          }
        >
          <AIWorkflowsCarousel />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary>
        <MainHeading
          id="05"
          title="Education"
          description="How i am progressing in my education"
          ref={educationRef}
        />
        <Suspense fallback={<EducationSkeleton />}>
          <Education />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary
        fallback={
          <div className="flex justify-center items-center py-20">
            <div className="text-center">
              <p className="text-txt-desc mb-4">Failed to load blogs section</p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-acc text-white rounded-lg hover:bg-acc/80 transition-colors"
              >
                Retry
              </button>
            </div>
          </div>
        }
      >
        <MainHeading
          id="06"
          title="My Blogs"
          description="I like to write blogs, especially on Medium"
          ref={blogsRef}
        />
        <Suspense
          fallback={
            <div className="max-w-6xl mx-auto mt-5 py-4 px-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
                {[...Array(6)].map((_, i) => (
                  <BlogCardSkeleton key={i} />
                ))}
              </div>
            </div>
          }
        >
          <Blogs />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary>
        <MainHeading
          id="06"
          title="Contact Me"
          description="Let's discuss your next project or collaboration"
          ref={contactRef}
        />
        <Suspense fallback={<SectionSkeleton className="min-h-[400px]" />}>
          <ContactForm />
        </Suspense>
      </ErrorBoundary>

      <ErrorBoundary>
        <Suspense fallback={<SectionSkeleton className="h-32" />}>
          <Footer ref={aboutRef} />
        </Suspense>
      </ErrorBoundary>

      <ScrollToTop />
    </>
  );
}
