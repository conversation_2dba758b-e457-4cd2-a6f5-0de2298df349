"use client";

import React from "react";

interface ActionButtonProps {
  text: string;
  icon: string;
  onClick?: () => void;
  colored?: boolean;
  link?: string;
  css?: string;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  text,
  icon,
  onClick,
  colored = false,
  link,
  css = ""
}) => {
  return (
    <a href={link} target="_blank" rel="noopener noreferrer">
      <button
        onClick={onClick}
        type="button"
        className={
          (colored
            ? "text-black bg-gradient-to-r from-white hover:to-acc active:to-acc to-gray-300 hover:bg-gradient-to-tl group "
            : " text-white ") +
          css +
          " font-medium rounded-xl max-sm:py-1 pl-4 pr-3 py-1.5 text-center transition-all duration-200 ease "
        }
      >
        {text}
        <i
          className={`inline ml-2 group-hover:animate-bounce fa-duotone fa-solid fa-${icon}`}
        />
      </button>
    </a>
  );
};

export default ActionButton;
