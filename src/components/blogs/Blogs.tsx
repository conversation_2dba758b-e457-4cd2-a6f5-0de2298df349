"use client";

import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { fetchMediumArticles, MediumArticle } from "../../lib/medium";
import BlogImageCard from "./BlogImageCard";
import Spinner from "../effects/Spinner";

const Blogs: React.FC = () => {
  const [blogs, setBlogs] = useState<MediumArticle[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [errorMessage, setErrorMessage] = useState("");

  useEffect(() => {
    async function loadBlogs() {
      try {
        setIsLoading(true);
        const articles = await fetchMediumArticles("anasaltaf");

        if (articles.length > 0) {
          setBlogs(articles);
        } else {
          setBlogs([]);
          setErrorMessage("No Blogs Found!");
        }
      } catch (e) {
        setErrorMessage(`Sorry! Unable to load medium blogs. Error: ${e}`);
      } finally {
        setIsLoading(false);
      }
    }
    loadBlogs();
  }, []);

  return (
    <motion.div
      className="max-w-6xl mx-auto mt-5 py-4 px-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {isLoading ? (
        <motion.div
          className="flex justify-center items-center h-max"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Spinner />
        </motion.div>
      ) : errorMessage ? (
        <motion.p
          className="text-white text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {errorMessage}
        </motion.p>
      ) : (
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          {blogs.map((blog, index) => (
            <BlogImageCard key={blog.id} blog={blog} index={index} />
          ))}
        </motion.div>
      )}
    </motion.div>
  );
};

export default Blogs;
