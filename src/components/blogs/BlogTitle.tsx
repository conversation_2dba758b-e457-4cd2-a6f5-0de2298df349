"use client";

import React from "react";
import { motion } from "framer-motion";

interface BlogTitleProps {
  id: number;
  title: string;
  description?: string;
}

const BlogTitle: React.FC<BlogTitleProps> = ({ id, title, description }) => {
  const boldWord = title.split(" ")[0] || "";
  const remainingTitle = title.split(" ").slice(1).join(" ") || "";

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2, ease: "linear" }}
    >
      <motion.h2
        className="text-lg text-left"
        whileHover={{
          color: "#10b981",
          transition: { duration: 0.15, ease: "linear" }
        }}
      >
        <motion.span
          className="relative bottom-0 -left-0.5 right-auto text-[0.7rem] font-light"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.05, duration: 0.2, ease: "linear" }}
        >
          {id > 10 ? id : `0${id}`}
        </motion.span>

        <motion.span
          className="font-medium"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1, duration: 0.2, ease: "linear" }}
        >
          {boldWord}
        </motion.span>
        
        <motion.span 
          className="font-light"
          initial={{ opacity: 0, x: -10 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          {" " + remainingTitle}
        </motion.span>
      </motion.h2>
      
      {description && (
        <motion.p 
          className="text-center font-light text-txt-desc text-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          {description}
        </motion.p>
      )}
    </motion.div>
  );
};

export default BlogTitle;
