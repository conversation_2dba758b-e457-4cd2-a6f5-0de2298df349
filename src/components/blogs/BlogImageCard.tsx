"use client";

import React from "react";
import { motion } from "framer-motion";
import ActionButton from "../buttons/ActionButton";
import BlogTitle from "./BlogTitle";
import { MediumArticle } from "../../lib/medium";

interface BlogImageCardProps {
  blog: MediumArticle;
  index?: number;
}

const BlogImageCard: React.FC<BlogImageCardProps> = ({ blog, index = 0 }) => {
  return (
    <motion.div
      className="border-1 hover:border-acc bg-black active:border-acc transition-border duration-150 ease border-fg-s rounded-xl shadow-sm shadow-black"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{
        duration: 0.3,
        delay: index * 0.05,
        ease: "linear"
      }}
      whileHover={{
        y: -5,
        transition: { duration: 0.15, ease: "linear" }
      }}
      whileTap={{ y: 0 }}
    >
      <motion.div
        className="relative rounded-xl h-48 bg-cover bg-center bg-no-repeat overflow-hidden"
        style={{ backgroundImage: `url(${blog.image})` }}
        whileHover={{ opacity: 0.9 }}
        transition={{ duration: 0.3 }}
      >
        <motion.div
          className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"
          initial={{ opacity: 0 }}
          whileHover={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        />

        <motion.div
          className="absolute bottom-0 right-0 bg-bg-p p-1 pb-0 inline-block rounded-tl-xl"
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.15 + index * 0.05, duration: 0.2, ease: "linear" }}
          whileHover={{ opacity: 0.8, transition: { duration: 0.15, ease: "linear" } }}
        >
          <ActionButton
            css=" !rounded-lg "
            colored={true}
            text="Explore it"
            link={blog.link}
            icon="up-right-from-square"
          />
        </motion.div>
      </motion.div>

      <motion.div
        className="relative w-full rounded-xl overflow-hidden p-0.5"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4 + index * 0.1 }}
      >
        <div className="p-4">
          <motion.h2
            className="text-xl mb-1 font-semibold"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 + index * 0.1 }}
          >
            <BlogTitle title={blog.title} id={blog.id} />
          </motion.h2>

          <motion.p
            className="text-gray-600 text-[0.8rem]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 + index * 0.1 }}
          >
            by{" "}
            <motion.a
              href={blog.url}
              target="_blank"
              rel="noopener noreferrer"
              className="underline italic text-txt-desc"
              whileHover={{
                color: "#10b981",
                transition: { duration: 0.2 }
              }}
            >
              {blog.author}
            </motion.a>
          </motion.p>

          <motion.p
            className="text-gray-600 text-[0.7rem] font-light"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.7 + index * 0.1 }}
          >
            {blog.date.split(" ").join(" • ")}
          </motion.p>

          <motion.p
            className="mt-2 text-txt-desc font-light text-sm line-clamp-4"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 + index * 0.1 }}
          >
            {blog.desc}
          </motion.p>

          <motion.a
            href={blog.link}
            target="_blank"
            rel="noopener noreferrer"
            className="mt-3 text-sm inline text-fg-s"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.9 + index * 0.1 }}
            whileHover={{
              color: "#10b981",
              transition: { duration: 0.2 }
            }}
          >
            Read More →
          </motion.a>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default BlogImageCard;
