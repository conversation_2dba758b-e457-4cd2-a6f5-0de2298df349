import React, { forwardRef } from "react";

interface MainHeadingProps {
  id: string;
  title: string;
  description?: string;
}

const MainHeading = forwardRef<HTMLDivElement, MainHeadingProps>(
  ({ id, title, description }, ref) => {
    const titleWords = title.split(" ");
    const titleLastWord = titleWords.slice(-1)[0];
    const titleFirstWords = titleWords.slice(0, -1).join(" ");

    return (
      <div className="pt-10 pb-5" ref={ref}>
        <h2 className="text-2xl text-center">
          <span className="relative -top-1.5 -left-1 bottom-auto right-auto text-[0.8rem] font-light">
            {id}
          </span>
          <span className="font-light">{titleFirstWords}</span>
          <span className="font-medium"> {titleLastWord}</span>
        </h2>
        <p className="text-center font-light text-txt-desc text-sm">
          {description || ""}
        </p>
      </div>
    );
  }
);

MainHeading.displayName = "MainHeading";

export default MainHeading;
