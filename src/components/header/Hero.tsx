"use client";

import React, { forwardRef } from "react";
import { motion } from "framer-motion";
import Navbar from "./Navbar";
import ActionButton from "../buttons/ActionButton";
import Socials from "../socials/Socials";
import TypingText from "../effects/TypingText";
import SafeImage from "../ui/SafeImage";

interface HeroProps {
  onNavClick: (section: string) => void;
}

const Hero = forwardRef<HTMLDivElement, HeroProps>(({ onNavClick }, ref) => {
  const handleResumeDownload = () => {
    const link = document.createElement("a");
    link.href = "/assets/docs/cv/Anas-Altaf-CV.pdf";
    link.download = "Anas-Altaf-CV.pdf";
    link.click();
    link.remove();
  };

  return (
    <div
      ref={ref}
      className="relative overflow-hidden h-screen w-full bg-bg-p/85 top-0 left-0 right-0 bottom-0 dark"
    >
      <div
        className="before:inset-0 before:content-[''] before:absolute
            before:bg-[url(/assets/images/bg-2.webp)] before:z-[-1] before:bg-cover before:bg-center before:bg-no-repeat md:before:bg-fixed"
      ></div>

      <Navbar onNavClick={onNavClick} />

      <div className="flex flex-row max-md:flex-col-reverse md:items-center justify-center md:justify-between pb-5 md:flex-1 px-[calc(3.5rem+1.2vw)] h-full w-full">
        <div className="space-y-5 max-md:pb-0 text-left max-md:flex flex-col justify-center items-center">
          <motion.h1
            className="max-md:hidden text-[calc(2.6rem+1.2vw)] font-semibold text-white w-[min(20,100)]"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            We turn ideas <br /> into Reality
          </motion.h1>

          <motion.p
            className="font-light text-white"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            Research • Analyzing • Solving
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <ActionButton
              text="Resume"
              colored={true}
              icon="arrow-down-to-line"
              onClick={handleResumeDownload}
            />
            <ActionButton
              text="Code Space"
              colored={false}
              icon="arrow-up-right-from-square"
              link="https://github.com/Anas-Altaf"
            />
          </motion.div>

          <div className="max-md:inline hidden w-min">
            <Socials />
          </div>
        </div>

        <div className="flex flex-col max-md:text-center md:flex-1 justify-around items-center max-md:px-0 pb-10">
          <motion.div
            className="relative size-55 rounded-full shadow-md shadow-black/50 overflow-hidden"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            whileHover={{
              filter: "grayscale(0%)",
              transition: { duration: 0.3 }
            }}
            style={{ filter: "grayscale(100%)" }}
          >
            <SafeImage
              src="/assets/images/personal_img_2.webp"
              alt="Anas Altaf - Personal Image"
              fill
              className="object-cover"
              priority
              sizes="220px"
            />
          </motion.div>

          <motion.h1
            className="text-5xl inline relative -bottom-3 font-extralight text-txt-desc"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.5 }}
          >
            Hi there!
          </motion.h1>

          <motion.h1
            className="text-3xl font-extralight"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.7 }}
          >
            <span className="text-txt-desc">I&apos;m</span>{" "}
            <span className="font-medium text-6xl">Anas Altaf</span>
          </motion.h1>

          <motion.div
            className="overflow-hidden"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.9 }}
          >
            <TypingText />
          </motion.div>

          <div className="md:hidden inline w-min">
            <Socials />
          </div>
        </div>
      </div>
    </div>
  );
});

Hero.displayName = "Hero";

export default Hero;
