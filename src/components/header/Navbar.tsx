"use client";

import React, { useState } from "react";
import { FaBars } from "react-icons/fa";
import SafeImage from "../ui/SafeImage";

interface NavbarProps {
  onNavClick: (section: string) => void;
}

const Navbar: React.FC<NavbarProps> = ({ onNavClick }) => {
  const navItems = [
    "Home",
    "Projects",
    "Skills",
    "AI Workflows",
    "Blogs",
    "Contact",
    "About",
  ];
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <div className="flex max-md:absolute flex-row justify-between items-center p-5 h-20 w-full top-0 left-0 right-0 z-50 bg-transparent">
      {/* Logo */}
      <div className="hidden md:block">
        <SafeImage
          src="/assets/images/personal-img-art.webp"
          alt="Anas Altaf - Logo"
          width={60}
          height={60}
          className="h-15 w-15 object-cover rounded-full"
          priority
        />
      </div>

      {/* Desktop Navigation Menu */}
      <div className="hidden md:block fixed top-5 left-1/2 transform -translate-x-1/2 transition-all duration-300 z-50">
        <ul className="flex flex-row justify-center items-center space-x-5 rounded-full bg-bg-p/70 backdrop-blur-md px-6 py-2 cursor-pointer border border-fg-s/50 shadow-lg shadow-black/20">
          {navItems.map((item, index) => (
            <li
              key={index}
              onClick={() => onNavClick(item)}
              className="cursor-pointer text-white font-medium hover:text-acc active:text-acc border-b-2 border-transparent hover:border-b-acc active:border-b-acc transition-all duration-200 ease"
            >
              {item}
            </li>
          ))}
        </ul>
      </div>

      {/* Mobile Nav Menu (Click to Toggle) */}
      {isMobileMenuOpen && (
        <div className="fixed top-0 left-0 rounded-b-xl w-full h-1/2 z-50 bg-bg-p/80 backdrop-blur-md shadow-lg md:hidden">
          <ul className="flex flex-col items-center py-4 text-2xl space-y-4">
            {navItems.map((item, index) => (
              <li
                key={index}
                onClick={() => {
                  onNavClick(item);
                  setIsMobileMenuOpen(false);
                }}
                className="cursor-pointer text-white font-medium hover:text-acc active:text-acc transition-all duration-200 ease"
              >
                {item}
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Mobile Nav Menu (Hamburger Icon) - Only on small devices */}
      <div className="md:hidden">
        <div
          className="fixed z-50 top-5 right-6 w-12 h-12 bg-bg-p/80 backdrop-blur-md rounded-full flex items-center justify-center cursor-pointer border border-fg-s/50 transition-all duration-300"
          onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        >
          <FaBars className="text-white text-xl" />
        </div>
      </div>

      {/* Hire Me Button */}
      <div className="hidden md:block">
        <button className="text-white rounded-xl bg-transparent border-2 px-4 hover:border-transparent active:border-transparent py-1 hover:bg-gradient-to-tl active:bg-gradient-to-tl from-white to-zinc-200 border-white cursor-pointer hover:bg-white active:bg-white hover:text-black active:text-black transition-all duration-300">
          Hire me
        </button>
      </div>
    </div>
  );
};

export default Navbar;
