"use client";

import React from "react";
import { motion } from "framer-motion";

interface EducationData {
  degree: string;
  institution: string;
  dateRange: string;
  location: string;
  country: string;
  eqfLevel: string;
  link: string;
  logo: string;
}

interface EducationCardProps {
  education: EducationData;
  index?: number;
}

const EducationCard: React.FC<EducationCardProps> = ({ education, index = 0 }) => {
  const isCurrent = education.dateRange.includes("Current");

  return (
    <motion.div
      className="border-2 bg-fg-p border-dashed border-fg-s p-6 mb-6 rounded-lg relative bg-[url('https://www.transparenttextures.com/patterns/diag-crosshatch.png')] bg-opacity-10 transition-transform duration-200 ease hover:shadow-lg"
      initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{
        duration: 0.4,
        delay: index * 0.1,
        ease: "linear"
      }}
      whileHover={{
        y: -3,
        transition: { duration: 0.15, ease: "linear" }
      }}
    >
      <div className="relative z-10">
        <motion.span
          className={
            isCurrent
              ? "text-acc text-sm border-1 border-acc/40 px-2 py-1 rounded-full"
              : "text-txt-desc text-sm border-1 border-fg-s/40 px-2 py-1 rounded-full"
          }
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.3 + index * 0.2, type: "spring" }}
          whileHover={{ opacity: 0.8 }}
        >
          {education.dateRange}
        </motion.span>

        <motion.div
          className="flex items-center my-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 + index * 0.2 }}
        >
          <motion.div
            className="size-12 rounded-full bg-bg-p items-center hidden md:flex justify-center mr-4"
            whileHover={{
              opacity: 0.8,
              transition: { duration: 0.2 }
            }}
          >
            <img src={education.logo} alt={education.institution} />
          </motion.div>

          <div>
            <motion.h3
              className="font-semibold text-xl text-white"
              whileHover={{
                color: "#10b981",
                transition: { duration: 0.2 }
              }}
            >
              {education.degree}
            </motion.h3>

            <motion.p
              className="text-txt-desc text-lg font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 + index * 0.2 }}
            >
              {education.institution}
            </motion.p>
          </div>
        </motion.div>

        <motion.div
          className="flex flex-wrap gap-2"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 + index * 0.2 }}
        >
          {[education.location, education.country, education.eqfLevel].map((item, idx) => (
            <span
              key={idx}
              className="text-txt-desc text-sm bg-fg-s px-2 py-1 rounded-full"
            >
              {item}
            </span>
          ))}
        </motion.div>
      </div>
    </motion.div>
  );
};

export default EducationCard;
