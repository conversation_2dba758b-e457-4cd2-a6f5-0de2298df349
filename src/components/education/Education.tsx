"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import EducationCard from "./EducationCard";

interface EducationData {
  degree: string;
  institution: string;
  dateRange: string;
  location: string;
  country: string;
  eqfLevel: string;
  link: string;
  logo: string;
}

// Education data is now fetched dynamically from the API
const staticEducationData: EducationData[] = [
  {
    degree: "B.S in Software Engineering",
    institution: "FAST National University of Computer and Emerging Sciences",
    dateRange: " 20 Aug 2022 – Current ",
    location: "City: Chiniot-Faisalabad",
    country: "Country: Pakistan",
    eqfLevel: "Level in EQF: EQF level 4",
    link: "https://cfd.nu.edu.pk",
    logo: "/assets/images/logos/fast-nuces.png",
  },
  {
    degree: "Intermediate in Computer Science",
    institution: "Superior Group of Colleges",
    dateRange: " 20 Sep 2020 – 10 Aug 2022 ",
    location: "City: Nankana Sahib",
    country: "Country: Pakistan",
    eqfLevel: "Level in EQF: EQF level 4",
    link: "https://superiorcolleges.edu.pk",
    logo: "/assets/images/logos/superior-college.png",
  },
  {
    degree: "Matric in Science",
    institution: "Center of Excellence Higher Secondary School",
    dateRange: " 8 Aug 2018 – 1 Aug 2020 ",
    location: "City: Jaranwala",
    country: "Country: Pakistan",
    eqfLevel: "Level in EQF: EQF level 4",
    link: "",
    logo: "/assets/images/logos/coe-gov.png",
  },
];

const Education: React.FC = () => {
  const [educationData, setEducationData] = useState<EducationData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEducation = async () => {
      try {
        const response = await fetch("/api/education");
        if (!response.ok) {
          throw new Error("Failed to fetch education");
        }
        const data = await response.json();
        setEducationData(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load education"
        );
        console.error("Error fetching education:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchEducation();
  }, []);

  if (loading) {
    return (
      <motion.div
        className="bg-bg-p p-8 min-h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-txt-desc">Loading education...</div>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="bg-bg-p p-8 min-h-screen"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-4xl mx-auto text-center">
          <div className="text-red-500">Error: {error}</div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="bg-bg-p p-8 min-h-screen"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <motion.div
        className="max-w-4xl mx-auto space-y-6"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
      >
        {educationData.map((edu, index) => (
          <EducationCard key={index} education={edu} index={index} />
        ))}
      </motion.div>
    </motion.div>
  );
};

export default Education;
