"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";

const TypingText: React.FC = () => {
  function startsWithVowel(word: string): boolean {
    const firstLetter = word.charAt(0).toLowerCase();
    return ["a", "e", "i", "o", "u"].includes(firstLetter);
  }

  const words = ["Web Developer", "Freelancer", "AI Enthusiast"];
  const typingSpeed = 100; // Typing speed in ms
  const deleteSpeed = 50; // Deleting speed in ms
  const pauseTime = 2000; // Pause before deleting

  const [text, setText] = useState("");
  const [index, setIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const currentWord = words[index];
    let timeout: NodeJS.Timeout;

    if (isDeleting) {
      timeout = setTimeout(() => {
        setText((prev) => prev.slice(0, -1));
      }, deleteSpeed);
    } else {
      timeout = setTimeout(() => {
        setText((prev) => currentWord.slice(0, prev.length + 1));
      }, typingSpeed);
    }

    if (!isDeleting && text === currentWord) {
      timeout = setTimeout(() => setIsDeleting(true), pauseTime);
    } else if (isDeleting && text === "") {
      setIsDeleting(false);
      setIndex((prev) => (prev + 1) % words.length);
    }

    return () => clearTimeout(timeout);
  }, [text, isDeleting, index, words]);

  return (
    <motion.h2
      className="text-2xl font-medium my-auto"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2, ease: "linear" }}
    >
      <span>{startsWithVowel(text) ? "An " : "A "}</span>
      <motion.span
        className="bg-gradient-to-r from-purple-400 to-green-400 bg-clip-text text-transparent"
        // key={text} // This will trigger re-animation when text changes
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.2, ease: "linear" }}
      >
        {text}
      </motion.span>
      <motion.span
        className="text-white font-light"
        animate={{ opacity: [1, 0, 1] }}
        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      >
        _
      </motion.span>
    </motion.h2>
  );
};

export default TypingText;
