"use client";

import React from "react";
import { motion } from "framer-motion";

// Project Card Skeleton
export const ProjectCardSkeleton: React.FC = () => {
  return (
    <div className="bg-fg-p p-6 rounded-xl shadow-md shadow-black">
      <div className="flex justify-between items-center mb-6">
        <div className="h-4 bg-fg-s rounded w-24 animate-pulse" />
        <div className="flex -space-x-1">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className="size-6 bg-fg-s rounded-full animate-pulse"
            />
          ))}
        </div>
      </div>
      
      <div className="grid sm:grid-cols-2 gap-2 sm:gap-10">
        <div className="w-full h-80 bg-fg-s rounded-xl animate-pulse" />
        <div className="flex flex-col justify-center space-y-4">
          <div className="h-8 bg-fg-s rounded w-3/4 animate-pulse" />
          <div className="space-y-2">
            <div className="h-4 bg-fg-s rounded animate-pulse" />
            <div className="h-4 bg-fg-s rounded w-5/6 animate-pulse" />
            <div className="h-4 bg-fg-s rounded w-4/6 animate-pulse" />
          </div>
        </div>
      </div>
    </div>
  );
};

// Blog Card Skeleton
export const BlogCardSkeleton: React.FC = () => {
  return (
    <div className="bg-fg-p rounded-xl overflow-hidden shadow-md">
      <div className="w-full h-48 bg-fg-s animate-pulse" />
      <div className="p-6 space-y-4">
        <div className="h-6 bg-fg-s rounded w-3/4 animate-pulse" />
        <div className="space-y-2">
          <div className="h-4 bg-fg-s rounded animate-pulse" />
          <div className="h-4 bg-fg-s rounded w-5/6 animate-pulse" />
        </div>
        <div className="flex justify-between items-center">
          <div className="h-4 bg-fg-s rounded w-20 animate-pulse" />
          <div className="h-4 bg-fg-s rounded w-16 animate-pulse" />
        </div>
      </div>
    </div>
  );
};

// Repo Card Skeleton
export const RepoCardSkeleton: React.FC = () => {
  return (
    <div className="bg-fg-p p-6 rounded-xl shadow-md mx-2">
      <div className="flex items-start justify-between mb-4">
        <div className="h-6 bg-fg-s rounded w-32 animate-pulse" />
        <div className="h-5 bg-fg-s rounded w-16 animate-pulse" />
      </div>
      <div className="space-y-3">
        <div className="h-4 bg-fg-s rounded animate-pulse" />
        <div className="h-4 bg-fg-s rounded w-4/5 animate-pulse" />
      </div>
      <div className="flex items-center justify-between mt-6">
        <div className="flex items-center space-x-4">
          <div className="h-4 bg-fg-s rounded w-12 animate-pulse" />
          <div className="h-4 bg-fg-s rounded w-12 animate-pulse" />
        </div>
        <div className="h-4 bg-fg-s rounded w-20 animate-pulse" />
      </div>
    </div>
  );
};

// Skills Section Skeleton
export const SkillsSkeleton: React.FC = () => {
  return (
    <div className="max-w-6xl mx-auto px-6 py-10">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="bg-fg-p p-6 rounded-xl">
            <div className="h-6 bg-fg-s rounded w-24 mb-4 animate-pulse" />
            <div className="space-y-3">
              {[...Array(4)].map((_, j) => (
                <div key={j} className="flex items-center space-x-3">
                  <div className="size-8 bg-fg-s rounded animate-pulse" />
                  <div className="h-4 bg-fg-s rounded flex-1 animate-pulse" />
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Education Timeline Skeleton
export const EducationSkeleton: React.FC = () => {
  return (
    <div className="max-w-4xl mx-auto px-6 py-10">
      <div className="space-y-8">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="flex items-start space-x-6">
            <div className="size-12 bg-fg-s rounded-full animate-pulse flex-shrink-0" />
            <div className="flex-1 space-y-3">
              <div className="h-6 bg-fg-s rounded w-48 animate-pulse" />
              <div className="h-5 bg-fg-s rounded w-32 animate-pulse" />
              <div className="space-y-2">
                <div className="h-4 bg-fg-s rounded animate-pulse" />
                <div className="h-4 bg-fg-s rounded w-5/6 animate-pulse" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Generic Section Skeleton
export const SectionSkeleton: React.FC<{ className?: string }> = ({
  className = "",
}) => {
  return (
    <motion.div
      className={`flex justify-center items-center py-20 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="text-center space-y-4">
        <div className="h-8 bg-fg-s rounded w-48 mx-auto animate-pulse" />
        <div className="h-4 bg-fg-s rounded w-64 mx-auto animate-pulse" />
      </div>
    </motion.div>
  );
};
