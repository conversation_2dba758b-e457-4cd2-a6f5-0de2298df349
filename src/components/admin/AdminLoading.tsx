import { Loader2 } from "lucide-react"

interface AdminLoadingProps {
  message?: string
}

export default function AdminLoading({ message = "Loading..." }: AdminLoadingProps) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-600" />
        <p className="text-gray-600">{message}</p>
      </div>
    </div>
  )
}
