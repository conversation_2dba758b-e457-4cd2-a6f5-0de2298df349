import { Alert<PERSON><PERSON>gle, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

interface AdminErrorProps {
  title?: string
  message?: string
  onRetry?: () => void
}

export default function AdminError({ 
  title = "Something went wrong",
  message = "An error occurred while loading the content.",
  onRetry 
}: AdminErrorProps) {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="max-w-md w-full">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{title}</AlertTitle>
          <AlertDescription className="mt-2">
            {message}
          </AlertDescription>
        </Alert>
        {onRetry && (
          <div className="mt-4 text-center">
            <Button onClick={onRetry} variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
