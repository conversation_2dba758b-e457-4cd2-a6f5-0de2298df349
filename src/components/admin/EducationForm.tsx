"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Save, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

const educationSchema = z.object({
  degree: z.string().min(1, "Degree is required"),
  institution: z.string().min(1, "Institution is required"),
  dateRange: z.string().min(1, "Date range is required"),
  location: z.string().min(1, "Location is required"),
  country: z.string().min(1, "Country is required"),
  eqfLevel: z.string().optional(),
  logo: z.string().url().optional().or(z.literal("")),
  link: z.string().url().optional().or(z.literal("")),
  current: z.boolean().default(false),
  order: z.number().default(0),
})

type EducationFormData = z.infer<typeof educationSchema>

interface EducationFormProps {
  educationId?: string
  initialData?: any
}

export default function EducationForm({ educationId, initialData }: EducationFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<EducationFormData>({
    resolver: zodResolver(educationSchema),
    defaultValues: initialData || {
      current: false,
      order: 0,
    },
  })

  const isEditing = !!educationId

  const onSubmit = async (data: EducationFormData) => {
    setLoading(true)
    try {
      const url = isEditing 
        ? `/api/admin/education/${educationId}`
        : "/api/admin/education"
      
      const method = isEditing ? "PATCH" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save education")
      }

      toast.success(`Education ${isEditing ? "updated" : "created"} successfully`)
      router.push("/admin/education")
    } catch (error: any) {
      toast.error(error.message || `Failed to ${isEditing ? "update" : "create"} education`)
      console.error("Error saving education:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/education">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? "Edit Education" : "Add Education"}
            </h1>
            <p className="text-gray-600 mt-1">
              {isEditing ? "Update education details" : "Add a new education entry to your portfolio"}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="max-w-2xl space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Education Information</CardTitle>
            <CardDescription>
              Enter the details about your educational background
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="degree">Degree</Label>
                <Input
                  id="degree"
                  {...register("degree")}
                  placeholder="e.g., B.S in Software Engineering"
                />
                {errors.degree && (
                  <p className="text-sm text-red-600 mt-1">{errors.degree.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="institution">Institution</Label>
                <Input
                  id="institution"
                  {...register("institution")}
                  placeholder="e.g., FAST National University"
                />
                {errors.institution && (
                  <p className="text-sm text-red-600 mt-1">{errors.institution.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="dateRange">Date Range</Label>
              <Input
                id="dateRange"
                {...register("dateRange")}
                placeholder="e.g., 20 Aug 2022 – Current"
              />
              {errors.dateRange && (
                <p className="text-sm text-red-600 mt-1">{errors.dateRange.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="location">Location</Label>
                <Input
                  id="location"
                  {...register("location")}
                  placeholder="e.g., City: Chiniot-Faisalabad"
                />
                {errors.location && (
                  <p className="text-sm text-red-600 mt-1">{errors.location.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  {...register("country")}
                  placeholder="e.g., Country: Pakistan"
                />
                {errors.country && (
                  <p className="text-sm text-red-600 mt-1">{errors.country.message}</p>
                )}
              </div>
            </div>

            <div>
              <Label htmlFor="eqfLevel">EQF Level (Optional)</Label>
              <Input
                id="eqfLevel"
                {...register("eqfLevel")}
                placeholder="e.g., Level in EQF: EQF level 4"
              />
              {errors.eqfLevel && (
                <p className="text-sm text-red-600 mt-1">{errors.eqfLevel.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="logo">Institution Logo URL (Optional)</Label>
              <Input
                id="logo"
                {...register("logo")}
                placeholder="https://example.com/logo.png"
                type="url"
              />
              {errors.logo && (
                <p className="text-sm text-red-600 mt-1">{errors.logo.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="link">Institution Website (Optional)</Label>
              <Input
                id="link"
                {...register("link")}
                placeholder="https://institution.edu"
                type="url"
              />
              {errors.link && (
                <p className="text-sm text-red-600 mt-1">{errors.link.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="current">Currently Studying</Label>
                <Switch
                  id="current"
                  checked={watch("current")}
                  onCheckedChange={(checked) => setValue("current", checked)}
                />
              </div>

              <div>
                <Label htmlFor="order">Display Order</Label>
                <Input
                  id="order"
                  type="number"
                  {...register("order", { valueAsNumber: true })}
                  placeholder="0"
                />
                {errors.order && (
                  <p className="text-sm text-red-600 mt-1">{errors.order.message}</p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex space-x-2">
          <Button type="submit" disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            {loading ? "Saving..." : "Save Education"}
          </Button>
          <Button type="button" variant="outline" asChild>
            <Link href="/admin/education">Cancel</Link>
          </Button>
        </div>
      </form>
    </div>
  )
}
