"use client"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Save, ArrowLeft } from "lucide-react"
import { toast } from "sonner"
import Link from "next/link"

const softSkillSchema = z.object({
  name: z.string().min(1, "Name is required"),
  order: z.number().default(0),
})

type SoftSkillFormData = z.infer<typeof softSkillSchema>

interface SoftSkillFormProps {
  skillId?: string
  initialData?: any
}

export default function SoftSkillForm({ skillId, initialData }: SoftSkillFormProps) {
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SoftSkillFormData>({
    resolver: zodResolver(softSkillSchema),
    defaultValues: initialData || {
      order: 0,
    },
  })

  const isEditing = !!skillId

  const onSubmit = async (data: SoftSkillFormData) => {
    setLoading(true)
    try {
      const url = isEditing 
        ? `/api/admin/soft-skills/${skillId}`
        : "/api/admin/soft-skills"
      
      const method = isEditing ? "PATCH" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to save soft skill")
      }

      toast.success(`Soft skill ${isEditing ? "updated" : "created"} successfully`)
      router.push("/admin/skills")
    } catch (error: any) {
      toast.error(error.message || `Failed to ${isEditing ? "update" : "create"} soft skill`)
      console.error("Error saving soft skill:", error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/admin/skills">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? "Edit Soft Skill" : "Create Soft Skill"}
            </h1>
            <p className="text-gray-600 mt-1">
              {isEditing ? "Update soft skill details" : "Add a new soft skill to your portfolio"}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="max-w-lg space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Soft Skill Information</CardTitle>
            <CardDescription>
              Enter the details about your soft skill
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="name">Skill Name</Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="e.g., Problem Solving, Communication, Leadership"
              />
              {errors.name && (
                <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="order">Display Order</Label>
              <Input
                id="order"
                type="number"
                {...register("order", { valueAsNumber: true })}
                placeholder="0"
              />
              {errors.order && (
                <p className="text-sm text-red-600 mt-1">{errors.order.message}</p>
              )}
              <p className="text-sm text-gray-500 mt-1">
                Lower numbers appear first in the list
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="flex space-x-2">
          <Button type="submit" disabled={loading}>
            <Save className="mr-2 h-4 w-4" />
            {loading ? "Saving..." : "Save Soft Skill"}
          </Button>
          <Button type="button" variant="outline" asChild>
            <Link href="/admin/skills">Cancel</Link>
          </Button>
        </div>
      </form>
    </div>
  )
}
