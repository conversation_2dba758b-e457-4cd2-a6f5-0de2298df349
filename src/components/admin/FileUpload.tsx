"use client"

import { useState, useCallback } from "react"
import { useDropzone } from "react-dropzone"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Upload, X, File, Image as ImageIcon, Video } from "lucide-react"
import { toast } from "sonner"

interface FileUploadProps {
  onUploadComplete: (files: any[]) => void
  maxFiles?: number
  maxSize?: number // in bytes
  acceptedTypes?: string[]
}

interface UploadFile {
  file: File
  preview?: string
  progress: number
  status: "pending" | "uploading" | "success" | "error"
  error?: string
}

export default function FileUpload({
  onUploadComplete,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = ["image/*", "video/*", "application/pdf"]
}: FileUploadProps) {
  const [uploadFiles, setUploadFiles] = useState<UploadFile[]>([])
  const [uploading, setUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      file,
      preview: file.type.startsWith("image/") ? URL.createObjectURL(file) : undefined,
      progress: 0,
      status: "pending" as const
    }))

    setUploadFiles(prev => [...prev, ...newFiles].slice(0, maxFiles))
  }, [maxFiles])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => {
      acc[type] = []
      return acc
    }, {} as Record<string, string[]>),
    maxSize,
    maxFiles,
    onDropRejected: (rejectedFiles) => {
      rejectedFiles.forEach(({ file, errors }) => {
        errors.forEach(error => {
          if (error.code === "file-too-large") {
            toast.error(`File ${file.name} is too large. Max size is ${maxSize / 1024 / 1024}MB`)
          } else if (error.code === "file-invalid-type") {
            toast.error(`File ${file.name} has invalid type`)
          }
        })
      })
    }
  })

  const removeFile = (index: number) => {
    setUploadFiles(prev => {
      const newFiles = [...prev]
      if (newFiles[index].preview) {
        URL.revokeObjectURL(newFiles[index].preview!)
      }
      newFiles.splice(index, 1)
      return newFiles
    })
  }

  const uploadFile = async (uploadFile: UploadFile, index: number) => {
    const formData = new FormData()
    formData.append("file", uploadFile.file)

    try {
      setUploadFiles(prev => {
        const newFiles = [...prev]
        newFiles[index] = { ...newFiles[index], status: "uploading", progress: 0 }
        return newFiles
      })

      const response = await fetch("/api/admin/media/upload", {
        method: "POST",
        body: formData,
      })

      if (!response.ok) {
        throw new Error("Upload failed")
      }

      const result = await response.json()

      setUploadFiles(prev => {
        const newFiles = [...prev]
        newFiles[index] = { ...newFiles[index], status: "success", progress: 100 }
        return newFiles
      })

      return result
    } catch (error) {
      setUploadFiles(prev => {
        const newFiles = [...prev]
        newFiles[index] = { 
          ...newFiles[index], 
          status: "error", 
          error: error instanceof Error ? error.message : "Upload failed"
        }
        return newFiles
      })
      throw error
    }
  }

  const handleUpload = async () => {
    if (uploadFiles.length === 0) return

    setUploading(true)
    const uploadedFiles = []

    try {
      for (let i = 0; i < uploadFiles.length; i++) {
        const uploadFile = uploadFiles[i]
        if (uploadFile.status === "pending") {
          try {
            const result = await uploadFile(uploadFile, i)
            uploadedFiles.push(result)
          } catch (error) {
            console.error(`Failed to upload ${uploadFile.file.name}:`, error)
          }
        }
      }

      if (uploadedFiles.length > 0) {
        onUploadComplete(uploadedFiles)
        setUploadFiles([])
      }
    } finally {
      setUploading(false)
    }
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith("image/")) return ImageIcon
    if (file.type.startsWith("video/")) return Video
    return File
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? "border-blue-500 bg-blue-50"
            : "border-gray-300 hover:border-gray-400"
        }`}
      >
        <input {...getInputProps()} />
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        {isDragActive ? (
          <p className="text-blue-600">Drop the files here...</p>
        ) : (
          <div>
            <p className="text-gray-600 mb-2">
              Drag & drop files here, or click to select files
            </p>
            <p className="text-sm text-gray-500">
              Max {maxFiles} files, up to {maxSize / 1024 / 1024}MB each
            </p>
          </div>
        )}
      </div>

      {uploadFiles.length > 0 && (
        <div className="space-y-3">
          <h3 className="font-medium">Files to upload ({uploadFiles.length})</h3>
          <div className="space-y-2">
            {uploadFiles.map((uploadFile, index) => {
              const FileIcon = getFileIcon(uploadFile.file)
              return (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        {uploadFile.preview ? (
                          <img
                            src={uploadFile.preview}
                            alt={uploadFile.file.name}
                            className="w-12 h-12 object-cover rounded"
                          />
                        ) : (
                          <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                            <FileIcon className="h-6 w-6 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {uploadFile.file.name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {formatFileSize(uploadFile.file.size)}
                        </p>
                        {uploadFile.status === "uploading" && (
                          <Progress value={uploadFile.progress} className="mt-2" />
                        )}
                        {uploadFile.status === "error" && (
                          <p className="text-xs text-red-600 mt-1">
                            {uploadFile.error}
                          </p>
                        )}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          variant={
                            uploadFile.status === "success"
                              ? "default"
                              : uploadFile.status === "error"
                              ? "destructive"
                              : uploadFile.status === "uploading"
                              ? "secondary"
                              : "outline"
                          }
                        >
                          {uploadFile.status}
                        </Badge>
                        {uploadFile.status === "pending" && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              variant="outline"
              onClick={() => setUploadFiles([])}
              disabled={uploading}
            >
              Clear All
            </Button>
            <Button
              onClick={handleUpload}
              disabled={uploading || uploadFiles.every(f => f.status !== "pending")}
            >
              {uploading ? "Uploading..." : "Upload Files"}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
