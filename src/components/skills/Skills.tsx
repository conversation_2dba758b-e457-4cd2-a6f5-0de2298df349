"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import SkillCard from "./SkillCard";

// Skills and soft skills are now fetched dynamically from the API
const staticSkillsData = [
  {
    title: "Frontend Development",
    description:
      "Skilled in creating dynamic UIs across web, mobile and desktop platforms.",
    stack: [
      "React",
      "Flutter",
      "Streamlit",
      "Swing",
      "Gradio",
      "Next.js",
      "Winforms",
    ],
  },
  {
    title: "AI and Automation",
    description:
      "Proficient in task automation and AI-powered workflow solutions.",
    stack: ["Python", "Selenium", "Socialbots", "make.com", "n8n"],
  },
  {
    title: "Backend Development",
    description:
      "Experienced in building robust and scalable server-side applications.",
    stack: ["Django", "Flask", "FastAPI", "Next.js", "MERN stack"],
  },
  {
    title: "Data Scraping and Analysis",
    description:
      "Skilled in extracting, processing and visualizing data for actionable insights.",
    stack: [
      "Selenium",
      "Scrapy",
      "BeautifulSoup",
      "Power BI",
      "Streamlit",
      "Pandas",
      "LLMs",
    ],
  },
];

const Skills: React.FC = () => {
  const [skillsData, setSkillsData] = useState<any[]>([]);
  const [softSkills, setSoftSkills] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSkills = async () => {
      try {
        const response = await fetch("/api/skills");
        if (!response.ok) {
          throw new Error("Failed to fetch skills");
        }
        const data = await response.json();
        setSkillsData(data.skills);
        setSoftSkills(data.softSkills);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load skills");
        console.error("Error fetching skills:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchSkills();
  }, []);

  if (loading) {
    return (
      <motion.section
        className="text-white pb-3 px-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-6xl mx-auto text-center">
          <div className="text-txt-desc">Loading skills...</div>
        </div>
      </motion.section>
    );
  }

  if (error) {
    return (
      <motion.section
        className="text-white pb-3 px-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        <div className="max-w-6xl mx-auto text-center">
          <div className="text-red-500">Error: {error}</div>
        </div>
      </motion.section>
    );
  }

  return (
    <motion.section
      className="text-white pb-3 px-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="max-w-6xl mx-auto">
        {/* Soft Skills Pills */}
        <motion.div
          className="mt-4 flex justify-center flex-wrap gap-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {softSkills.map((skill, index) => (
            <span
              key={index}
              className="px-3 py-0.5 bg-acc/5 border-1 border-acc/40 text-white rounded-full text-sm font-light"
            >
              {skill}
            </span>
          ))}
        </motion.div>

        {/* Skills Grid */}
        <motion.div
          className="mt-10 grid gap-6 md:grid-cols-2"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          {skillsData.map((skill, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.5,
                delay: 0.6 + index * 0.1,
              }}
            >
              <SkillCard {...skill} />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default Skills;
