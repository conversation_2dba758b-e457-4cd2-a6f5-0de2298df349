"use client";

import React from "react";
import { motion } from "framer-motion";
import Skill<PERSON>ard from "./SkillCard";

const softSkills = [
  "Problem Solving",
  "Critical Thinking",
  "Teamwork",
  "Adaptability",
  "Communication",
  "Quick Learning",
  "New Tech Enthusiast",
];

const skillsData = [
  {
    title: "Frontend Development",
    description:
      "Skilled in creating dynamic UIs across web, mobile and desktop platforms.",
    stack: [
      "React",
      "Flutter",
      "Streamlit",
      "Swing",
      "Gradio",
      "Next.js",
      "Winforms",
    ],
  },
  {
    title: "AI and Automation",
    description:
      "Proficient in task automation and AI-powered workflow solutions.",
    stack: ["Python", "Selenium", "Socialbots", "make.com", "n8n"],
  },
  {
    title: "Backend Development",
    description:
      "Experienced in building robust and scalable server-side applications.",
    stack: ["Django", "Flask", "FastAPI", "Next.js", "MERN stack"],
  },
  {
    title: "Data Scraping and Analysis",
    description:
      "Skilled in extracting, processing and visualizing data for actionable insights.",
    stack: [
      "Selenium",
      "Scrapy",
      "BeautifulSoup",
      "Power BI",
      "Streamlit",
      "Pandas",
      "LLMs",
    ],
  },
];

const Skills: React.FC = () => {
  return (
    <motion.section
      className="text-white pb-3 px-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      <div className="max-w-6xl mx-auto">
        {/* Soft Skills Pills */}
        <motion.div
          className="mt-4 flex justify-center flex-wrap gap-4"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          {softSkills.map((skill, index) => (
            <span
              key={index}
              className="px-3 py-0.5 bg-acc/5 border-1 border-acc/40 text-white rounded-full text-sm font-light"
            >
              {skill}
            </span>
          ))}
        </motion.div>

        {/* Skills Grid */}
        <motion.div
          className="mt-10 grid gap-6 md:grid-cols-2"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
        >
          {skillsData.map((skill, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.5,
                delay: 0.6 + index * 0.1
              }}
            >
              <SkillCard {...skill} />
            </motion.div>
          ))}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default Skills;
