"use client";

import React from "react";
import { motion } from "framer-motion";

interface SkillCardProps {
  title: string;
  description: string;
  stack: string[];
}

const SkillCard: React.FC<SkillCardProps> = ({ title, description, stack }) => {
  return (
    <motion.div
      className="bg-fg-p bg-gradient-to-r from-fg-s/50 to-fg-p/50 hover:bg-gradient-to-tl active:bg-gradient-to-tl rounded-xl p-6 h-50 flex flex-col justify-evenly items-start space-y-1 border-1 border-fg-s hover:transform active:transform hover:-translate-y-1 active:-translate-y-1 transition-all duration-200 ease overflow-hidden shadow-md group shadow-black/50"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "linear" }}
      whileHover={{
        y: -5,
        transition: { duration: 0.15, ease: "linear" }
      }}
      whileTap={{ y: 0 }}
    >
      <motion.div
        className="flex items-center gap-2"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.1, duration: 0.2, ease: "linear" }}
      >
        <motion.span
          className="text-acc text-xl"
          whileHover={{
            opacity: 0.8,
            transition: { duration: 0.15, ease: "linear" }
          }}
        >
          <i className="fa-solid fa-circle-check bg-transparent rounded-full shadow-acc shadow-xl"></i>
        </motion.span>

        <motion.h3
          className="text-lg font-semibold"
          whileHover={{
            color: "#10b981",
            transition: { duration: 0.15, ease: "linear" }
          }}
        >
          {title}
        </motion.h3>
      </motion.div>

      <motion.p
        className="text-txt-desc text-sm mt-2"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        {description}
      </motion.p>

      <motion.div
        className="flex flex-wrap gap-2 mt-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        {stack.map((item, index) => (
          <span
            key={index}
            className="text-acc font-light text-sm bg-fg-p px-2 py-1 rounded-full"
          >
            {item}
          </span>
        ))}
      </motion.div>
    </motion.div>
  );
};

export default SkillCard;
