import Head from "next/head";
import { Metadata } from "next";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: "website" | "article" | "profile";
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  noIndex?: boolean;
}

export const generateMetadata = ({
  title = "Anas Altaf | Portfolio",
  description = "Passionate developer specializing in web development, Scripting, AI and Automation. Explore my projects, skills, and experience.",
  keywords = "Anas Altaf, Portfolio, Web Developer, React, Next.js, Python, AI, Automation, Full Stack Developer, Software Engineer",
  image = "/assets/images/og-image.jpg",
  url = "https://anas-altaf.github.io",
  type = "website",
  publishedTime,
  modifiedTime,
  author = "Anas Altaf",
  section,
  noIndex = false,
}: SEOProps): Metadata => {
  const baseUrl = "https://anas-altaf.github.io";
  const fullUrl = url.startsWith("http") ? url : `${baseUrl}${url}`;
  const fullImageUrl = image.startsWith("http") ? image : `${baseUrl}${image}`;

  return {
    title,
    description,
    keywords,
    authors: [{ name: author }],
    creator: author,
    publisher: author,
    robots: noIndex ? "noindex,nofollow" : "index,follow",
    
    // Open Graph
    openGraph: {
      title,
      description,
      url: fullUrl,
      siteName: "Anas Altaf Portfolio",
      images: [
        {
          url: fullImageUrl,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale: "en_US",
      type: type as any,
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(section && { section }),
    },

    // Twitter
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [fullImageUrl],
      creator: "@anasaltaf", // Replace with actual Twitter handle
    },

    // Additional meta tags
    other: {
      "theme-color": "#10b981",
      "color-scheme": "dark",
      "msapplication-TileColor": "#10b981",
      "msapplication-config": "/browserconfig.xml",
    },

    // Icons
    icons: {
      icon: [
        { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
        { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      ],
      apple: [
        { url: "/apple-touch-icon.png", sizes: "180x180", type: "image/png" },
      ],
      other: [
        { rel: "mask-icon", url: "/safari-pinned-tab.svg", color: "#10b981" },
      ],
    },

    // Manifest
    manifest: "/site.webmanifest",

    // Verification
    verification: {
      google: "your-google-verification-code", // Replace with actual verification code
      // yandex: "your-yandex-verification-code",
      // bing: "your-bing-verification-code",
    },

    // Alternates
    alternates: {
      canonical: fullUrl,
      languages: {
        "en-US": fullUrl,
      },
    },
  };
};

// JSON-LD Structured Data
export const generateStructuredData = (params: {
  name?: string;
  jobTitle?: string;
  description?: string;
  url?: string;
  image?: string;
  email?: string;
  sameAs?: string[];
} = {}) => {
  const {
    name = "Anas Altaf",
    jobTitle = "Full Stack Developer",
    description = "Passionate developer specializing in web development, Scripting, AI and Automation.",
    url = "https://anas-altaf.github.io",
    image = "/assets/images/personal_img_2.webp",
    email = "<EMAIL>",
    sameAs = [
      "https://github.com/Anas-Altaf",
      "https://linkedin.com/in/anas-altaf",
      "https://medium.com/@anasaltaf",
    ],
  } = params;
  const baseUrl = "https://anas-altaf.github.io";
  const fullImageUrl = image.startsWith("http") ? image : `${baseUrl}${image}`;

  return {
    "@context": "https://schema.org",
    "@type": "Person",
    name,
    jobTitle,
    description,
    url,
    image: fullImageUrl,
    email,
    sameAs,
    knowsAbout: [
      "Web Development",
      "React",
      "Next.js",
      "TypeScript",
      "Python",
      "Artificial Intelligence",
      "Machine Learning",
      "Full Stack Development",
      "Software Engineering",
    ],
    alumniOf: {
      "@type": "EducationalOrganization",
      name: "Your University", // Replace with actual university
    },
    worksFor: {
      "@type": "Organization",
      name: "Freelancer",
    },
  };
};

// Website structured data
export const generateWebsiteStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Anas Altaf Portfolio",
    url: "https://anas-altaf.github.io",
    description: "Portfolio website showcasing web development projects, skills, and experience.",
    author: {
      "@type": "Person",
      name: "Anas Altaf",
    },
    potentialAction: {
      "@type": "SearchAction",
      target: "https://anas-altaf.github.io/search?q={search_term_string}",
      "query-input": "required name=search_term_string",
    },
  };
};
