"use client";

import React from "react";
import SafeImage from "../ui/SafeImage";

interface RepoCardProps {
  id: number;
  category: string[];
  name: string;
  description: string;
  link: string;
  icon: string;
}

const RepoCard: React.FC<RepoCardProps> = ({
  id,
  category,
  name,
  description,
  link,
  icon
}) => {
  return (
    <div className="py-4 px-2">
      <a
        href={link}
        target="_blank"
        rel="noreferrer"
        className="bg-fg-p bg-gradient-to-r from-fg-s/50 to-fg-p/50 hover:bg-gradient-to-tl active:bg-gradient-to-tl hover:border-acc active:border-acc rounded-xl p-6 h-42.5 flex flex-col justify-evenly items-start space-y-1 border-1 border-fg-s hover:transform active:transform hover:-translate-y-1 active:-translate-y-1 transition-all duration-300 shadow-md group shadow-black/50"
      >
        <p className="text-2xl font-extralight">{id < 10 ? "0" + id : id}</p>
        <div className="flex flex-row items-center justify-between w-full">
          <div className="space-x-0.5">
            {category &&
              category.slice(0, 2).map((item, index) => (
                <span
                  key={index}
                  className="text-white font-extralight bg-bg-p text-[0.5rem] rounded-full px-2 pb-1 pt-0.5 text-center align-middle"
                >
                  {item}
                </span>
              ))}
          </div>
          <SafeImage
            src={`https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/${icon}/${icon}-original.svg`}
            alt={`${icon} language icon`}
            width={28}
            height={28}
            className="h-7 w-7 object-contain"
          />
        </div>
        <div>
          <h1 className="text-white text-sm font-light">{name}</h1>
          <p className="font-light text-[0.8rem] text-txt-desc overflow-hidden">
            {description}
          </p>
        </div>
      </a>
    </div>
  );
};

export default RepoCard;
