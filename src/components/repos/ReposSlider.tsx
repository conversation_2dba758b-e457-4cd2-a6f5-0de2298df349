"use client";

import React, { useEffect, useState } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { getGithubRepos, ProcessedRepo } from "../../lib/github";
import RepoCard from "./RepoCard";
import Spinner from "../effects/Spinner";

const ReposSlider: React.FC = () => {
  const settings = {
    dots: false,
    infinite: true,
    slidesToShow: 4,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    cssEase: "linear",
    pauseOnHover: true,
    draggable: true,
    swipe: true,
    touchMove: true,
    focusOnSelect: false,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 3,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          autoplaySpeed: 2000,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
        },
      },
    ],
  };

  // States
  const [githubData, setGithubData] = useState<ProcessedRepo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  async function fetchGithubRepos() {
    setIsLoading(true);
    try {
      const repos = await getGithubRepos();
      if (repos.length > 0) {
        setGithubData(repos);
      } else {
        setGithubData([]);
        setErrorMessage("No Repositories Found!");
      }
    } catch (e) {
      setErrorMessage(`Sorry! Unable to load github repositories. Error: ${e}`);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    fetchGithubRepos();
  }, []);

  return (
    <div className="slider-container overflow-hidden">
      {isLoading ? (
        <div className="flex justify-center items-center h-max">
          <Spinner />
        </div>
      ) : errorMessage ? (
        <p className="text-white text-center">{errorMessage}</p>
      ) : (
        <div>
          <Slider {...settings}>
            {githubData.map((repo) => (
              <RepoCard key={repo.id} {...repo} />
            ))}
          </Slider>
        </div>
      )}
    </div>
  );
};

export default ReposSlider;
