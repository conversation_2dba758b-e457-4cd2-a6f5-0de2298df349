"use client";

import React, { useState, useEffect } from "react";
import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import { motion } from "framer-motion";
import WorkflowCard from "./WorkflowCard";
import Spinner from "../effects/Spinner";

interface AIWorkflow {
  id: string;
  title: string;
  description: string;
  category: string;
  tools: string[];
  complexity: "Beginner" | "Intermediate" | "Advanced";
  duration: string;
  icon: string;
  features: string[];
  useCase: string;
}

const AIWorkflowsCarousel: React.FC = () => {
  const [workflows, setWorkflows] = useState<AIWorkflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const settings = {
    dots: true,
    infinite: true,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 4000,
    cssEase: "ease-in-out",
    pauseOnHover: true,
    draggable: true,
    swipe: true,
    touchMove: true,
    focusOnSelect: false,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          autoplaySpeed: 3000,
        },
      },
    ],
  };

  useEffect(() => {
    // Simulate API call - in real implementation, this would fetch from your admin panel
    const fetchWorkflows = async () => {
      try {
        setLoading(true);
        // Simulated delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const mockWorkflows: AIWorkflow[] = [
          {
            id: "1",
            title: "WhatsApp AI Assistant",
            description: "Intelligent WhatsApp bot with voice recognition, task management, and email integration using LangChain.",
            category: "Conversational AI",
            tools: ["Python", "LangChain", "FastAPI", "OpenAI", "WhatsApp API"],
            complexity: "Advanced",
            duration: "2-3 weeks",
            icon: "🤖",
            features: [
              "Voice message transcription",
              "Task management integration",
              "Gmail automation",
              "Context-aware responses"
            ],
            useCase: "Personal productivity and communication automation"
          },
          {
            id: "2", 
            title: "LLM Data Annotation Pipeline",
            description: "Automated pipeline for preparing and annotating datasets for Large Language Model training with quality control.",
            category: "Data Processing",
            tools: ["Python", "Streamlit", "Pandas", "SQLite", "Plotly"],
            complexity: "Intermediate",
            duration: "1-2 weeks",
            icon: "📊",
            features: [
              "Multi-format data ingestion",
              "Collaborative annotation",
              "Quality assurance metrics",
              "Export to ML frameworks"
            ],
            useCase: "ML model training and data preparation"
          },
          {
            id: "3",
            title: "Research Paper Scraping Bot",
            description: "Automated system for scraping, organizing, and analyzing academic papers from research databases.",
            category: "Web Automation",
            tools: ["Python", "Selenium", "BeautifulSoup", "Pandas", "Jupyter"],
            complexity: "Intermediate",
            duration: "1 week",
            icon: "📚",
            features: [
              "Multi-source paper extraction",
              "Metadata organization",
              "PDF download management",
              "Research trend analysis"
            ],
            useCase: "Academic research and literature review automation"
          },
          {
            id: "4",
            title: "Computer Vision Media Controller",
            description: "Hand gesture and face detection system for controlling media playback without touching devices.",
            category: "Computer Vision",
            tools: ["Python", "OpenCV", "MediaPipe", "NumPy"],
            complexity: "Advanced",
            duration: "2 weeks",
            icon: "👋",
            features: [
              "Real-time gesture recognition",
              "Face detection auto-pause",
              "Voice command integration",
              "Multi-platform support"
            ],
            useCase: "Hands-free media control and accessibility"
          },
          {
            id: "5",
            title: "Social Media Automation Suite",
            description: "Comprehensive automation toolkit for social media management, content scheduling, and engagement tracking.",
            category: "Social Automation",
            tools: ["Python", "APIs", "Selenium", "Schedule", "Analytics"],
            complexity: "Intermediate",
            duration: "2-3 weeks",
            icon: "📱",
            features: [
              "Multi-platform posting",
              "Content scheduling",
              "Engagement analytics",
              "Automated responses"
            ],
            useCase: "Social media management and growth automation"
          },
          {
            id: "6",
            title: "Email Intelligence System",
            description: "AI-powered email management with smart categorization, auto-responses, and priority detection.",
            category: "Email Automation",
            tools: ["Python", "NLP", "Gmail API", "Machine Learning"],
            complexity: "Advanced",
            duration: "3-4 weeks",
            icon: "📧",
            features: [
              "Smart email categorization",
              "Priority detection",
              "Automated responses",
              "Sentiment analysis"
            ],
            useCase: "Professional email management and productivity"
          }
        ];
        
        setWorkflows(mockWorkflows);
      } catch (err) {
        setError("Failed to load AI workflows");
        console.error("Error fetching workflows:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchWorkflows();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-10">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-acc text-white rounded-lg hover:bg-acc/80 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <motion.div
      className="py-10 px-6"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="max-w-7xl mx-auto">
        <div className="slider-container overflow-hidden">
          <Slider {...settings}>
            {workflows.map((workflow) => (
              <div key={workflow.id} className="px-3">
                <WorkflowCard {...workflow} />
              </div>
            ))}
          </Slider>
        </div>
      </div>
    </motion.div>
  );
};

export default AIWorkflowsCarousel;
