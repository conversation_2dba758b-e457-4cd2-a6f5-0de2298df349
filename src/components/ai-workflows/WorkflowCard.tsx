"use client";

import React from "react";
import { motion } from "framer-motion";

interface WorkflowCardProps {
  id: string;
  title: string;
  description: string;
  category: string;
  tools: string[];
  complexity: "Beginner" | "Intermediate" | "Advanced";
  duration: string;
  icon: string;
  features: string[];
  useCase: string;
}

const WorkflowCard: React.FC<WorkflowCardProps> = ({
  title,
  description,
  category,
  tools,
  complexity,
  duration,
  icon,
  features,
  useCase,
}) => {
  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case "Beginner":
        return "bg-green-500/10 text-green-400 border-green-500/20";
      case "Intermediate":
        return "bg-yellow-500/10 text-yellow-400 border-yellow-500/20";
      case "Advanced":
        return "bg-red-500/10 text-red-400 border-red-500/20";
      default:
        return "bg-gray-500/10 text-gray-400 border-gray-500/20";
    }
  };

  return (
    <motion.div
      className="bg-bg-s/50 backdrop-blur-sm border border-acc/20 rounded-xl p-6 h-full min-h-[500px] flex flex-col"
      whileHover={{ 
        scale: 1.02,
        boxShadow: "0 20px 40px rgba(0, 255, 255, 0.1)",
        borderColor: "rgba(0, 255, 255, 0.3)"
      }}
      transition={{ duration: 0.3 }}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="text-4xl mb-2">{icon}</div>
        <div className="flex flex-col items-end space-y-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getComplexityColor(complexity)}`}>
            {complexity}
          </span>
          <span className="text-xs text-txt-desc bg-acc/10 px-2 py-1 rounded">
            {duration}
          </span>
        </div>
      </div>

      {/* Title and Category */}
      <div className="mb-4">
        <h3 className="text-xl font-bold text-white mb-2 line-clamp-2">
          {title}
        </h3>
        <span className="text-sm text-acc font-medium bg-acc/10 px-2 py-1 rounded">
          {category}
        </span>
      </div>

      {/* Description */}
      <p className="text-txt-desc text-sm mb-4 line-clamp-3 flex-grow">
        {description}
      </p>

      {/* Use Case */}
      <div className="mb-4">
        <h4 className="text-sm font-semibold text-white mb-2">Use Case:</h4>
        <p className="text-xs text-txt-desc italic">
          {useCase}
        </p>
      </div>

      {/* Features */}
      <div className="mb-4">
        <h4 className="text-sm font-semibold text-white mb-2">Key Features:</h4>
        <ul className="space-y-1">
          {features.slice(0, 3).map((feature, index) => (
            <li key={index} className="text-xs text-txt-desc flex items-start">
              <span className="text-acc mr-2 mt-0.5">•</span>
              <span className="line-clamp-1">{feature}</span>
            </li>
          ))}
          {features.length > 3 && (
            <li className="text-xs text-acc">
              +{features.length - 3} more features
            </li>
          )}
        </ul>
      </div>

      {/* Tools */}
      <div className="mt-auto">
        <h4 className="text-sm font-semibold text-white mb-2">Tech Stack:</h4>
        <div className="flex flex-wrap gap-1">
          {tools.slice(0, 4).map((tool, index) => (
            <span
              key={index}
              className="text-xs bg-acc/10 text-acc px-2 py-1 rounded border border-acc/20"
            >
              {tool}
            </span>
          ))}
          {tools.length > 4 && (
            <span className="text-xs bg-gray-500/10 text-gray-400 px-2 py-1 rounded border border-gray-500/20">
              +{tools.length - 4}
            </span>
          )}
        </div>
      </div>

      {/* Action Button */}
      <motion.button
        className="mt-4 w-full bg-acc/10 hover:bg-acc/20 text-acc border border-acc/30 rounded-lg py-2 px-4 text-sm font-medium transition-all duration-300"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        Learn More
      </motion.button>
    </motion.div>
  );
};

export default WorkflowCard;
