"use client";

import React from "react";
import { motion } from "framer-motion";

interface FeatureListProps {
  heading: string;
  features: string[];
}

const FeatureList: React.FC<FeatureListProps> = ({ heading, features }) => {
  return (
    <motion.section
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: "linear" }}
    >
      <ul>
        {features.map((feature, index) => (
          <motion.li
            className="text-txt-desc mb-2"
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{
              duration: 0.2,
              delay: index * 0.05,
              ease: "linear"
            }}
            whileHover={{
              x: 5,
              color: "#10b981",
              transition: { duration: 0.15, ease: "linear" }
            }}
          >
            ⪧ {feature}
          </motion.li>
        ))}
      </ul>
    </motion.section>
  );
};

export default FeatureList;
