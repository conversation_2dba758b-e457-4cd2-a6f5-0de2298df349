"use client";

import React from "react";
import { motion } from "framer-motion";
import ProjectCard from "./ProjectCard";

interface Project {
  pName: string;
  desc: string;
  img: string;
  video?: string;
  images?: string[];
  icons: string[];
  category: string;
  link: string;
  content: string[];
  isCurrent?: boolean;
}

const projects: Project[] = [
  {
    pName: "LLM Annotator",
    desc: "A comprehensive tool for annotating and managing datasets for Large Language Model training with advanced labeling capabilities.",
    img: "/assets/images/projects/llm_annotator_1.webp",
    video: "",
    images: [
      "/assets/images/projects/llm_annotator_1.webp",
      "/assets/images/projects/llm_annotator_2.webp",
    ],
    icons: ["python", "streamlit", "pandas", "numpy", "jupyter", "git", "vscode"],
    category: "Machine Learning and Data Annotation",
    link: "https://github.com/Anas-Altaf/LLM-Annotator",
    content: [
      "Dataset Management: Organize and manage large datasets for LLM training",
      "Advanced Labeling: Multi-class and multi-label annotation capabilities",
      "Quality Control: Built-in validation and quality assurance tools",
      "Export Options: Multiple format support for different ML frameworks",
      "Collaborative Features: Team-based annotation with role management",
      "Progress Tracking: Real-time monitoring of annotation progress",
      "Integration Ready: API support for seamless workflow integration",
    ],
    isCurrent: false,
  },
  {
    pName: "Motion Media - Control Media Play with Hand Gestures",
    desc: "A Python application that allows you to control media players using hand gestures, face detection, and voice commands.",
    img: "/assets/images/projects/motion_media_ss_1.jpg",
    video: "/assets/videos/projects/motion_media_vid.mp4",
    images: [],
    icons: ["python", "numpy", "pypi", "opencv", "git", "vscode"],
    category: "Computer Vision and Gesture Recognition",
    link: "https://github.com/Anas-Altaf/Motion_Media",
    content: [
      "Gesture Control: Play/pause, seek, volume, screenshot using hand gestures",
      "Face Detection: Auto-pauses media when you look away from the screen",
      "Voice Commands: Natural language control for media playback",
      "Multi-Platform Support: Works with various media players and applications",
      "Real-time Processing: Low-latency gesture recognition for smooth control",
      "Customizable Gestures: Configure your own gesture mappings",
      "Privacy-Focused: All processing happens locally on your device",
    ],
    isCurrent: false,
  },
  {
    pName: "WhatPoint - AI-Powered WhatsApp Assistant",
    desc: "App to WhatsApp into an intelligent assistant with voice, task and email management and more using LangChain and LLMs.",
    img: "/assets/images/projects/whatpoint.webp",
    video: "/assets/videos/projects/mld_vid.mp4",
    images: [],
    icons: ["python", "fastapi", "pypi", "android", "git", "vscode"],
    category: "AI Agents and Automation",
    link: "https://github.com/Anas-Altaf/WhatPoint",
    content: [
      "WhatsApp Integration: Seamlessly responds to text and voice messages",
      "Voice Recognition: Transcribes audio messages to text for processing",
      "Task Management: Create, read, update and delete personal tasks",
      "Gmail Integration: Send emails and retrieve recent inbox messages",
      "LangChain Framework: Advanced AI conversation capabilities",
      "Multi-Modal Support: Handle text, voice, and image inputs",
      "Context Awareness: Maintains conversation context for better responses",
    ],
    isCurrent: false,
  },
  {
    pName: "Multilingual Dictionary",
    desc: "A comprehensive Java-based dictionary application supporting multiple languages with advanced search capabilities and morphological analysis.",
    img: "/assets/images/projects/mld_ss_1.webp",
    video: "/assets/videos/projects/mld_vid.mp4",
    images: [
      "/assets/images/projects/mld_ss_1.webp",
      "/assets/images/projects/mld_ss_2.webp",
    ],
    icons: ["java", "intellij", "github", "git", "maven", "mysql", "apache"],
    category: "Desktop Development with Java",
    link: "https://github.com/Anas-Altaf/Multilingual-Dictionary",
    content: [
      "Multi-language word lookups by word, root, or meaning.",
      "Fuzzy matching and intelligent word segmentation.",
      "Morphological analysis, part-of-speech tagging, and lemmatization.",
      "MySQL database for scalable storage with CRUD operations.",
      "Professional interface with Java Swing and FlatLaf.",
    ],
    isCurrent: false,
  },
  {
    pName: "Neurl-PS Scrapper",
    desc: "A robust scrapper for https://nips.papers.cc , it could be helpful if you are a researcher or training your LLMs.",
    img: "/assets/images/projects/nips_ss_1.webp",
    video: "/assets/videos/projects/nips_vid.mp4",
    images: [
      "/assets/images/projects/nips_ss_1.webp",
      "/assets/images/projects/nips_ss_2.webp",
    ],
    icons: [
      "python",
      "jupyter",
      "selenium",
      "pandas",
      "streamlit",
      "git",
      "pycharm",
    ],
    category: "Web Scraping and Data Science",
    link: "https://github.com/Anas-Altaf/Neurl-PS-Scraper_py",
    content: [
      "Neurl-PS-Scraper_py is a Python-based tool to easily scrape NeurIPS papers.",
      "Features an intuitive GUI for easy navigation and searching of papers.",
      "Efficient Scraping: Fetches paper metadata and PDFs quickly.",
      "Progress Tracking: Allows real-time monitoring of the scraping process.",
      "Metadata Storage: Saves and manages paper details for future reference.",
      "Run the tool with either the CLI version or the GUI version.",
      "Search for papers, download PDFs, and view metadata.",
    ],
    isCurrent: false,
  },
  {
    pName: "Railway Management System",
    desc: "A comprehensive C# application designed to streamline railway operations by managing train schedules, reservations, and user information.",
    img: "/assets/images/projects/rm.webp",
    video: "",
    images: [
      "/assets/images/projects/rm_1.webp",
      "/assets/images/projects/rm_2.webp",
    ],
    icons: [
      "csharp",
      "dotnetcore",
      "visualstudio",
      "microsoftsqlserver",
      "git",
    ],
    category: "Desktop Application Development",
    link: "https://github.com/Anas-Altaf/Railway-Management-System",
    content: [
      "Train Management: Add, update, and delete train schedules with real-time tracking",
      "Reservation System: Complete booking system with seat selection and payment processing",
      "User Management: Role-based access control for passengers, staff, and administrators",
      "Database Integration: Robust SQL Server integration for data persistence and reporting",
      "Reporting Module: Generate comprehensive reports for operations and financial analysis",
      "Security Features: Encrypted user authentication and secure data handling",
      "Intuitive Interface: User-friendly C# application with responsive design patterns",
    ],
    isCurrent: false,
  }
];

const Projects: React.FC = () => (
  <motion.div
    className="flex flex-col items-center space-y-10 py-10 px-6 mx-auto max-w-4xl"
    style={{
      perspective: "1500px", // Enhanced perspective for better 3D effect
      perspectiveOrigin: "center center"
    }}
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.4, ease: "linear" }}
  >
    {projects.map(
      (project, idx) =>
        project && <ProjectCard key={idx} id={idx + 1} {...project} />
    )}
  </motion.div>
);

export default Projects;
