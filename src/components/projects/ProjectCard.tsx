"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X } from "lucide-react";
import { createPortal } from "react-dom";
import ActionButton from "../buttons/ActionButton";
import FeatureList from "./FeatureList";
import Status from "../effects/Status";
import SafeImage from "../ui/SafeImage";

interface ProjectCardProps {
  id: number;
  pName: string;
  desc: string;
  img: string;
  video?: string;
  images?: string[];
  icons: string[];
  category: string;
  link: string;
  content: string[];
  isCurrent?: boolean;
}

const ProjectCard: React.FC<ProjectCardProps> = ({
  id,
  pName,
  desc,
  img,
  video,
  images,
  icons,
  category,
  link,
  content,
  isCurrent,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before rendering portal
  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <div className="relative">
      {/* Clickable Card */}
      <motion.div
        className="block z-10 bg-fg-p pr-6 pt-6 max-sm:p-6 rounded-xl shadow-md shadow-black cursor-pointer hover:shadow-fg-s active:shadow-fg-s hover:shadow-lg active:shadow-lg transition-all duration-300 ease-in-out bg-gradient-to-r from-fg-p to-fg-s/20 hover:bg-gradient-to-tl active:bg-gradient-to-tl"
        initial={{ scale: 1 }}
        animate={{ scale: isExpanded ? 1.05 : 1 }}
        whileHover={{ scale: 1.05 }}
        onClick={() => setIsExpanded(true)}
      >
        <div className="flex justify-between max-sm:flex-col max:sm gap-2 max-sm:items-start items-center mb-6 sm:pl-6">
          <h4 className="text-txt-desc text-sm font-medium uppercase tracking-wide">
            {id < 10 ? "0" + id : id} | {category}
          </h4>
          <div className="flex max-sm:self-center -space-x-1 scale-x-[-1]">
            {icons
              .map((icon) => (
                <div key={icon} title={icon} className="size-6 scale-x-[-1] rounded-full p-0.5 bg-stone-200 shadow-md shadow-gray-900">
                  <SafeImage
                    src={`https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/${icon}/${icon}-original.svg`}
                    alt={icon}
                    width={24}
                    height={24}
                    className="w-full h-full object-contain scale-x-[-1]"
                  />
                </div>
              ))
              .reverse()}
          </div>
        </div>
        <div className="grid sm:grid-cols-2 gap-2 sm:gap-10">
          <div>
            <SafeImage
              src={img}
              alt={`Project ${id}`}
              width={400}
              height={320}
              className="w-full h-80 object-cover rounded-tr-xl max-sm:rounded-xl rounded-bl-xl"
            />
          </div>
          <div className="flex flex-col justify-center">
            {isCurrent && <Status text={"Under Development"} color={"acc"} />}
            <h1 className="text-2xl font-medium text-white mb-2">
              <i className="fa-duotone fa-thin fa-bullseye-pointer hover:fa-beat-fade"></i>{" "}
              {pName}
            </h1>
            <p className="text-gray-300 text-sm md:pr-20 max-md:pb-6">{desc}</p>
          </div>
        </div>
      </motion.div>

      {/* Modal with Animation - Using Portal */}
      {mounted &&
        createPortal(
          <AnimatePresence>
            {isExpanded && (
              <>
                {/* Full Background Blur */}
                <motion.div
                  className="fixed inset-0 bg-black/70 bg-opacity-50 backdrop-blur-sm z-40"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={() => setIsExpanded(false)} // Click outside to close
                />

                {/* Expanding Modal with Scrollable Content */}
                <motion.div
                  className="fixed z-50 top-1/2 left-1/2 md:w-6/10 w-full max-w-5xl max-h-[90vh] bg-bg-p max-md:px-1 p-4 rounded-xl shadow-xl transform -translate-x-1/2 -translate-y-1/2 overflow-hidden"
                  initial={{ scale: 0.7, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.7, opacity: 0 }}
                  transition={{ type: "spring", stiffness: 120, damping: 15 }}
                >
                  {/* Close Button */}
                  <button
                    className="absolute top-4 right-4 p-2 rounded-full bg-fg-s active:bg-fg-s/20 hover:bg-fg-s/20 transition duration-100 ease"
                    onClick={() => setIsExpanded(false)}
                  >
                    <X size={24} className="z-45" />
                  </button>

                  {/* Scrollable Content */}
                  <div
                    className="h-[80vh] overflow-y-auto px-2
                    [&::-webkit-scrollbar]:w-2
                    [&::-webkit-scrollbar-track]:rounded-full
                  [&::-webkit-scrollbar-track]:bg-fg-s
                    [&::-webkit-scrollbar-thumb]:rounded-full
                  [&::-webkit-scrollbar-thumb]:bg-bg-p"
                  >
                    {video && (
                      <motion.video
                        src={video}
                        autoPlay
                        loop
                        muted
                        className="w-full max-h-96 max-sm:h-3/10 object-cover rounded-md border-1 border-txt-desc"
                        transition={{ type: "spring", stiffness: 100, damping: 12 }}
                        style={{ pointerEvents: "none" }}
                      />
                    )}
                    <hr className="border-1 border-acc mt-3" />
                    {/* Additional Images */}
                    <div className="grid sm:grid-cols-2 gap-2 m-3">
                      {images &&
                        images.map((image, index) => (
                          <SafeImage
                            key={index}
                            src={image}
                            alt={`Project ${id} Image ${index}`}
                            width={400}
                            height={256}
                            className="w-full size-4/4 object-cover rounded-lg"
                          />
                        ))}
                    </div>

                    <h2 className="text-3xl mb-4">{pName}</h2>
                    <div className="text-txt-desc">
                      <FeatureList heading={pName} features={content} />
                    </div>
                    {/* Icons */}
                    <div className="mt-4 flex gap-2 flex-wrap">
                      {icons.map((icon) => (
                        <div key={icon} className="w-8 h-8 p-0.5 bg-stone-200 shadow-md shadow-gray-900 rounded-full">
                          <SafeImage
                            src={`https://cdn.jsdelivr.net/gh/devicons/devicon@latest/icons/${icon}/${icon}-original.svg`}
                            alt={icon}
                            width={32}
                            height={32}
                            className="w-full h-full object-contain"
                          />
                        </div>
                      ))}
                    </div>

                    {/* Visit Link Button */}
                    <div className="mt-6">
                      <ActionButton
                        link={link}
                        text="View Project"
                        icon="arrow-up-right-from-square"
                        colored={true}
                      />
                    </div>
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>,
          document.body
        )}
    </div>
  );
};

export default ProjectCard;
