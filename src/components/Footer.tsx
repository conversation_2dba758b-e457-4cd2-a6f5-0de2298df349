"use client";

import React, { forwardRef, useState } from "react";
import { motion } from "framer-motion";
import Socials from "./socials/Socials";
import SafeImage from "./ui/SafeImage";

const Footer = forwardRef<HTMLDivElement>((_props, ref) => {
  const [year] = useState(new Date().getFullYear());

  const services = [
    "Frontend Development",
    "AI & Automation",
    "Backend Solutions",
    "Data Analysis & Scraping",
  ];

  const support = [
    "Problem Solving",
    "Critical Thinking",
    "Teamwork",
    "Adaptability",
  ];

  return (
    <motion.footer
      className="bg-fg-p text-white py-10 pt-5 px-6"
      ref={ref}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <motion.p
        className="text-xl text-center pb-15"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        👋 Thanks for stopping by! Hope this visit was inspiring
      </motion.p>

      <div className="max-w-6xl mx-auto grid md:grid-cols-2 gap-8">
        {/* About Section */}
        <motion.div
          className="text-center md:text-left"
          initial={{ opacity: 0, x: -30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.4, delay: 0.2, ease: "linear" }}
        >
          <SafeImage
            src="/assets/images/personal-img.webp"
            alt="Anas Altaf - Personal Image"
            width={80}
            height={80}
            className="size-20 rounded-full md:mx-0 mx-auto object-cover hover:opacity-80 transition-opacity duration-150 ease"
          />

          <motion.h1
            className="text-2xl font-bold"
            
          >
            Anas Altaf
          </motion.h1>

          <motion.p
            className="mt-2 text-txt-desc text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.3, ease: "linear" }}
          >
            Passionate developer specializing in web development, Scripting, AI
            and Automation.
          </motion.p>

          <motion.div
            className="mt-4 flex justify-center md:justify-start"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.3, ease: "linear" }}
          >
            <Socials />
          </motion.div>

          <motion.p
            className="mt-4 text-txt-desc text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.5, duration: 0.3, ease: "linear" }}
          >
            &copy; <span>{year}</span> All Rights Reserved.
          </motion.p>
        </motion.div>

        {/* Services & Support Section */}
        <motion.div
          className="grid grid-cols-2 gap-4 justify-center"
          initial={{ opacity: 0, x: 30 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            <motion.p
           
            >
              Services
            </motion.p>
            <ul className="mt-2 space-y-1 text-txt-desc text-sm">
              {services.map((item, index) => (
                <motion.li
                  className="hover:text-white active:text-white focus:white text-sm cursor-pointer"
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6 + index * 0.1 }}
                  
                >
                  <a>{item}</a>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
          >
            <motion.p
             
            >
              Support
            </motion.p>
            <ul className="mt-2 space-y-1 text-txt-desc text-sm">
              {support.map((item, index) => (
                <motion.li
                  className="hover:text-white active:text-white text-sm cursor-pointer"
                  key={index}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.7 + index * 0.1 }}
                  
                >
                  <a>{item}</a>
                </motion.li>
              ))}
            </ul>
          </motion.div>
        </motion.div>
      </div>
    </motion.footer>
  );
});

Footer.displayName = "Footer";

export default Footer;
