"use client";

import React, { useState } from "react";
import Image from "next/image";

interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  priority?: boolean;
  sizes?: string;
  placeholder?: "blur" | "empty";
  blurDataURL?: string;
  fallbackSrc?: string;
  onLoad?: () => void;
  onError?: () => void;
}

const SafeImage: React.FC<SafeImageProps> = ({
  src,
  alt,
  width,
  height,
  className = "",
  fill = false,
  priority = false,
  sizes,
  placeholder,
  blurDataURL,
  fallbackSrc,
  onLoad,
  onError,
  ...props
}) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [useNextImage, setUseNextImage] = useState(true);

  const handleError = () => {
    console.log(`Image failed to load: ${imgSrc}`);
    setHasError(true);
    setIsLoading(false);

    if (fallbackSrc && imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc);
      setHasError(false);
      setIsLoading(true);
    } else if (useNextImage) {
      // Fallback to regular img tag
      setUseNextImage(false);
      setHasError(false);
      setIsLoading(true);
    } else {
      onError?.();
    }
  };

  const handleLoad = () => {
    console.log(`Image loaded successfully: ${imgSrc}`);
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  // If image failed to load and no fallback, return empty div
  if (hasError && !fallbackSrc && !useNextImage) {
    return (
      <div className={`${className} bg-fg-s/20 rounded-lg flex items-center justify-center`}>
        <span className="text-txt-desc text-sm">Image not available</span>
      </div>
    );
  }

  return (
    <>
      {isLoading && (
        <div className={`${className} bg-fg-s/20 animate-pulse rounded-lg`} />
      )}

      {useNextImage ? (
        <Image
          src={imgSrc}
          alt={alt}
          width={width}
          height={height}
          fill={fill}
          priority={priority}
          sizes={sizes}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
          onLoad={handleLoad}
          onError={handleError}
          unoptimized={true}
          {...props}
        />
      ) : (
        <img
          src={imgSrc}
          alt={alt}
          width={width}
          height={height}
          className={`${className} ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300 ${fill ? 'w-full h-full object-cover' : ''}`}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}
    </>
  );
};

export default SafeImage;
