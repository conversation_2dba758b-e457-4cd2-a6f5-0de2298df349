"use client";

import { useEffect } from "react";
import { usePerformanceMonitor } from "@/hooks/usePerformanceMonitor";

interface PerformanceMonitorProps {
  children: React.ReactNode;
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ children }) => {
  // Initialize performance monitoring
  usePerformanceMonitor({
    enableLogging: process.env.NODE_ENV === "development",
    enableAnalytics: process.env.NODE_ENV === "production",
    thresholds: {
      lcp: 2500, // Good: <= 2.5s
      fid: 100,  // Good: <= 100ms
      cls: 0.1,  // Good: <= 0.1
    },
  });

  useEffect(() => {
    // Monitor memory usage (if available)
    if ('memory' in performance) {
      const memoryInfo = (performance as any).memory;
      console.log('Memory Usage:', {
        used: Math.round(memoryInfo.usedJSHeapSize / 1048576) + ' MB',
        total: Math.round(memoryInfo.totalJSHeapSize / 1048576) + ' MB',
        limit: Math.round(memoryInfo.jsHeapSizeLimit / 1048576) + ' MB',
      });
    }

    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      try {
        const longTaskObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.duration > 50) { // Tasks longer than 50ms
              console.warn('Long task detected:', {
                duration: Math.round(entry.duration),
                startTime: Math.round(entry.startTime),
                name: entry.name,
              });
            }
          });
        });
        longTaskObserver.observe({ entryTypes: ['longtask'] });

        return () => longTaskObserver.disconnect();
      } catch (error) {
        console.warn('Long task monitoring not supported:', error);
      }
    }
  }, []);

  return <>{children}</>;
};

export default PerformanceMonitor;
