"use client";

import React from "react";
import SocialHandle from "./SocialHandle";

const SocialIconsTest: React.FC = () => {
  const testIcons = [
    { iconName: "github", link: "https://github.com/test" },
    { iconName: "linkedin", link: "https://linkedin.com/test" },
    { iconName: "whatsapp", link: "https://wa.me/test" },
    { iconName: "x-twitter", link: "https://x.com/test" },
    { iconName: "medium", link: "https://medium.com/test" },
  ];

  return (
    <div className="p-8 bg-bg-p">
      <h2 className="text-white text-xl mb-4">Social Icons Test</h2>
      <div className="flex gap-4 flex-wrap">
        {testIcons.map((icon, index) => (
          <div key={index} className="flex flex-col items-center gap-2">
            <SocialHandle iconName={icon.iconName} link={icon.link} />
            <span className="text-txt-desc text-xs">{icon.iconName}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SocialIconsTest;
