import React, { useEffect, useState } from "react";

interface SocialHandleProps {
  iconName: string;
  link: string;
}

const SocialHandle: React.FC<SocialHandleProps> = ({ iconName, link }) => {
  const [fontLoaded, setFontLoaded] = useState(false);

  useEffect(() => {
    // Check if FontAwesome is loaded
    const checkFontAwesome = () => {
      const testElement = document.createElement('i');
      testElement.className = 'fa-brands fa-github';
      testElement.style.position = 'absolute';
      testElement.style.left = '-9999px';
      document.body.appendChild(testElement);

      const computedStyle = window.getComputedStyle(testElement);
      const fontFamily = computedStyle.getPropertyValue('font-family');

      document.body.removeChild(testElement);

      if (fontFamily.includes('Font Awesome') || fontFamily.includes('FontAwesome')) {
        setFontLoaded(true);
      } else {
        // Retry after a short delay
        setTimeout(checkFontAwesome, 100);
      }
    };

    // Initial check
    if (document.readyState === 'complete') {
      checkFontAwesome();
    } else {
      window.addEventListener('load', checkFontAwesome);
      return () => window.removeEventListener('load', checkFontAwesome);
    }
  }, []);

  // Fallback text for each icon
  const getFallbackText = (iconName: string) => {
    const fallbacks: { [key: string]: string } = {
      'medium': 'M',
      'linkedin': 'in',
      'github': 'GH',
      'whatsapp': 'WA',
      'x-twitter': 'X',
      'twitter': 'T'
    };
    return fallbacks[iconName] || iconName.charAt(0).toUpperCase();
  };

  return (
    <a href={link} target="_blank" rel="noopener noreferrer">
      <button className="border-b-2 border-transparent hover:border-b-acc active:border-b-acc hover:border-b-2 active:border-b-2">
        {fontLoaded ? (
          <i
            className={`text-xl hover:text-gray-200 active:text-gray-200 cursor-pointer rounded-xl fa-brands fa-${iconName}`}
            style={{
              display: 'inline-block',
              width: '20px',
              height: '20px',
              textAlign: 'center',
              lineHeight: '20px'
            }}
          />
        ) : (
          <span
            className="hover:text-gray-200 active:text-gray-200 cursor-pointer rounded-xl inline-block w-5 h-5 text-center leading-5 bg-fg-s/50 text-xs font-bold"
            title={iconName}
          >
            {getFallbackText(iconName)}
          </span>
        )}
      </button>
    </a>
  );
};

export default SocialHandle;
